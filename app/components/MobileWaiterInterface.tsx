"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useReducer, useMemo } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { useOrderV4 } from '@/lib/hooks/use-order-v4';
import { useTableDB } from '@/lib/hooks/useTableDB';
import { useStaffMenuV4 } from '@/lib/hooks/useStaffMenuV4';
import { useToast } from '@/components/ui/use-toast';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent } from '@/components/ui/card';

// Icons
import {
  Search,
  ShoppingCart,
  Plus,
  Minus,
  X,
  Check,
  Utensils
} from 'lucide-react';

// Types
import type { MenuItem, Addon, Category } from '@/lib/db/v4-menu-service';
import type { OrderItem, OrderAddon, NewOrder } from '@/lib/types';
import type { OrderType } from '@/lib/types/order-types';
import type { PizzaQuarter } from '@/lib/types/pizza-types';
import { TableLayout as Table } from '@/lib/db/table-db';

// Order state interface
interface OrderState {
  _id: string;
  id: string;
  type: string;
  orderType: OrderType;
  status: string;
  tableId: string;
  items: OrderItem[];
  total: number;
  createdAt: string;
  updatedAt: string;
  schemaVersion: string;
  notes: string;
}

// UI state interface
interface UiState {
  selectedCategory: string;
  selectedItemForAddons: string | null;
  selectedItemSizes: {[key: string]: string};
  itemNotes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  searchQuery: string;
  showCart: boolean;
  lastAddedItem: string | null;
}

// Order actions
type OrderAction =
  | { type: 'INITIALIZE_ORDER' }
  | { type: 'ADD_ITEM', payload: { item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string } }
  | { type: 'ADD_CUSTOM_PIZZA', payload: { quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average' } }
  | { type: 'INCREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'DECREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'REMOVE_ITEM', payload: { itemId: string } }
  | { type: 'SET_TABLE', payload: { tableId: string } }
  | { type: 'SET_NOTES', payload: { notes: string } };

// Initial states
const initialOrderState: OrderState = {
  _id: "",
  id: "",
  type: "order_document",
  orderType: "dine-in",
  status: "pending",
  tableId: "",
  items: [],
  total: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  schemaVersion: "v4.0",
  notes: ""
};

const initialUiState: UiState = {
  selectedCategory: "",
  selectedItemForAddons: null,
  selectedItemSizes: {},
  itemNotes: {},
  selectedAddons: {},
  searchQuery: "",
  showCart: false,
  lastAddedItem: null
};

// Calculate total helper
const calculateTotal = (items: OrderItem[]): number => {
  return items.reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const addonsTotal = item.addons?.reduce((sum, addon) => sum + (addon.price * item.quantity), 0) || 0;
    return total + itemTotal + addonsTotal;
  }, 0);
};

// Order reducer
const orderReducer = (state: OrderState, action: OrderAction): OrderState => {
  switch (action.type) {
    case 'INITIALIZE_ORDER':
      return { ...initialOrderState };
      
    case 'ADD_ITEM': {
      const { item, size, addons, notes, categoryId } = action.payload;
      const price = item.prices[size] || Object.values(item.prices)[0] || 0;
      const uniqueId = `order_item_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: item.id,
        name: item.name,
        size: size,
        price: price,
        quantity: 1,
        addons: addons,
        notes: notes,
        categoryId: categoryId
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'ADD_CUSTOM_PIZZA': {
      const { quarters, size, notes, categoryId, pricingMethod } = action.payload;
      const uniqueId = `custom_pizza_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      let price = 0;
      if (pricingMethod === 'max') {
        price = Math.max(...quarters.map(q => q.price));
      } else {
        price = quarters.reduce((sum, q) => sum + q.price, 0) / quarters.length;
      }
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: 'custom_pizza',
        name: `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`,
        size: size,
        price: price,
        quantity: 1,
        addons: [],
        notes: notes,
        categoryId: categoryId,
        compositeType: 'pizza_quarters',
        quarters: quarters
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'INCREMENT_QUANTITY': {
      const updatedItems = state.items.map(item =>
        item.id === action.payload.itemId
          ? { ...item, quantity: item.quantity + 1 }
          : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'DECREMENT_QUANTITY': {
      const updatedItems = state.items.map(item =>
        item.id === action.payload.itemId && item.quantity > 1
          ? { ...item, quantity: item.quantity - 1 }
          : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'REMOVE_ITEM': {
      const updatedItems = state.items.filter(item => item.id !== action.payload.itemId);
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'SET_ORDER_TYPE':
      return { ...state, orderType: action.payload.orderType };
      
    case 'SET_TABLE':
      return { ...state, tableId: action.payload.tableId };

    case 'SET_NOTES':
      return { ...state, notes: action.payload.notes };
      
    default:
      return state;
  }
};

// Helper functions
const requiresTable = (orderType: OrderType): boolean => orderType === 'dine-in';
const requiresCustomerInfo = (orderType: OrderType): boolean => orderType === 'delivery' || orderType === 'takeaway';

// MenuItemCard component (matches NewOrderingInterface)
interface MenuItemCardProps {
  item: MenuItem;
  categoryId: string;
  isSelected: boolean;
  selectedSize: string | undefined;
  lastAddedItem: string | null;
  onAddItem: (item: MenuItem, size: string) => void;
}

const MenuItemCard: React.FC<MenuItemCardProps> = ({
  item,
  categoryId,
  isSelected,
  selectedSize,
  lastAddedItem,
  onAddItem
}) => {
  const handleSizeButtonClick = useCallback((e: React.MouseEvent, size: string) => {
    e.stopPropagation();
    onAddItem(item, size);
  }, [item, onAddItem]);

  return (
    <Card className={`transition-colors ${isSelected ? "ring-2 ring-primary/40" : ""}`}>
      <CardContent className="p-3">
        <div className="flex justify-between items-start mb-2">
          <div className="flex-1">
            <h3 className="font-medium text-sm truncate">{item.name}</h3>
            {item.description && (
              <p className="text-xs text-muted-foreground line-clamp-1 mt-1">
                {item.description}
              </p>
            )}
          </div>
        </div>
        <div className="flex flex-wrap gap-1.5">
          {Object.entries(item.prices).map(([size, price]) => {
            const isItemSizeSelected = isSelected && selectedSize === size;
            const isJustAdded = lastAddedItem === `${item.id}-${size}`;

            return (
              <Button
                key={size}
                size="sm"
                variant={isItemSizeSelected ? "default" : isJustAdded ? "secondary" : "outline"}
                onClick={(e) => handleSizeButtonClick(e, size)}
                className="h-8 px-2 text-xs"
              >
                <div className="flex flex-col items-center">
                  <span className="font-medium">
                    {size === 'default' ? 'Standard' : size}
                  </span>
                  <span className="font-medium tabular-nums">
                    {price as number} DA
                  </span>
                </div>
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

// MenuItemsGrid component
interface MenuItemsGridProps {
  categories: Category[] | undefined;
  selectedCategory: string;
  searchQuery: string;
  staffMenuItems: MenuItem[] | undefined;
  selectedItemForAddons: string | null;
  selectedItemSizes: {[key: string]: string};
  lastAddedItem: string | null;
  onAddItem: (item: MenuItem, size: string) => void;
}

const MenuItemsGrid: React.FC<MenuItemsGridProps> = ({
  categories,
  selectedCategory,
  searchQuery,
  staffMenuItems,
  selectedItemForAddons,
  selectedItemSizes,
  lastAddedItem,
  onAddItem
}) => {
  const currentCategory = categories?.find(cat => cat.id === selectedCategory);

  if (!currentCategory) return null;

  // Filter items based on search
  const filteredItems = currentCategory.items?.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  // Add staff menu items if they match the search
  const staffItems = staffMenuItems?.filter(item =>
    searchQuery && (
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  ) || [];

  const allItems = [...filteredItems, ...staffItems];

  if (allItems.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          {searchQuery ? 'Aucun article trouvé' : 'Aucun article dans cette catégorie'}
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-3">
      {allItems.map((item) => {
        const selectedSize = selectedItemSizes[item.id];
        const isSelected = selectedSize && selectedItemForAddons === `${item.id}-${selectedSize}`;

        return (
          <MenuItemCard
            key={item.id}
            item={item}
            categoryId={currentCategory.id}
            isSelected={isSelected}
            selectedSize={selectedSize}
            lastAddedItem={lastAddedItem}
            onAddItem={onAddItem}
          />
        );
      })}
    </div>
  );
};

export default function MobileWaiterInterface() {
  // Hooks
  const { user } = useAuth();
  const { categories, isLoading: menuLoading, isReady: menuReady } = useMenuV4();
  const { tables, isLoading: tablesLoading, isReady: tablesReady } = useTableDB();
  const { createOrder, isLoading: ordersLoading } = useOrderV4();
  const { staffMenuItems } = useStaffMenuV4();
  const { toast } = useToast();

  // State
  const [orderState, dispatch] = useReducer(orderReducer, initialOrderState);
  const [uiState, setUiState] = useState<UiState>(initialUiState);
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);

  // Helper functions for addon/note keys (matches NewOrderingInterface)
  const getAddonKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);
  const getItemNoteKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);

  // Set initial category when categories load
  useEffect(() => {
    if (categories?.length > 0 && !uiState.selectedCategory) {
      setUiState(prev => ({ ...prev, selectedCategory: categories[0].id }));
    }
  }, [categories, uiState.selectedCategory]);

  // Handle adding item (matches NewOrderingInterface flow)
  const handleAddItem = useCallback((item: MenuItem, size: string) => {
    const categoryId = uiState.selectedCategory;

    // Get current addons and notes for this item+size
    const addonKey = getAddonKey(item.id, size);
    const noteKey = getItemNoteKey(item.id, size);
    const selectedAddons = uiState.selectedAddons[addonKey] || new Set();
    const itemNotes = uiState.itemNotes[noteKey] || '';

    // Convert addons to OrderAddon format
    const category = categories?.find(cat => cat.id === categoryId);
    const addonObjects: OrderAddon[] = Array.from(selectedAddons).map(addonId => {
      const addon = category?.supplements?.find(s => s.id === addonId);
      return addon ? {
        id: addon.id,
        name: addon.name,
        price: addon.prices?.[size] || addon.prices?.['default'] || 0
      } : null;
    }).filter(Boolean) as OrderAddon[];

    // Add item to order
    dispatch({
      type: 'ADD_ITEM',
      payload: {
        item,
        size,
        addons: addonObjects,
        notes: itemNotes,
        categoryId: categoryId
      }
    });

    // Set last added item for visual feedback
    setUiState(prev => ({ ...prev, lastAddedItem: `${item.id}-${size}` }));

    // Open customization panel after adding item
    setUiState(prev => ({
      ...prev,
      selectedItemForAddons: `${item.id}-${size}`,
      selectedItemSizes: { ...prev.selectedItemSizes, [item.id]: size }
    }));
  }, [uiState.selectedAddons, uiState.itemNotes, uiState.selectedCategory, getAddonKey, getItemNoteKey, categories, dispatch]);

  // Place order function (simplified for dine-in only)
  const handlePlaceOrder = useCallback(async () => {
    if (!orderState.items?.length) return;
    if (!orderState.tableId) return;

    setIsPlacingOrder(true);

    try {
      const newOrder: NewOrder = {
        tableId: orderState.tableId,
        orderType: 'dine-in',
        status: 'pending',
        items: orderState.items.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
        total: orderState.total,
        notes: orderState.notes,
        paymentStatus: 'unpaid',
        createdBy: user?.name || (user as any)?.username || 'unknown',
        createdByName: user?.name || 'Personnel Inconnu'
      };

      await createOrder(newOrder);

      toast({
        title: "Commande créée",
        description: "La commande a été créée avec succès.",
      });

      // Reset order
      dispatch({ type: 'INITIALIZE_ORDER' });
      setUiState(initialUiState);

    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: "Erreur",
        description: "Impossible de créer la commande. Veuillez réessayer.",
        variant: "destructive"
      });
    } finally {
      setIsPlacingOrder(false);
    }
  }, [orderState, user, createOrder, toast]);

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Mobile Header */}
      <div className="flex-shrink-0 p-3 border-b bg-background/95 backdrop-blur">
        <div className="flex items-center justify-between mb-3">
          <h1 className="text-lg font-semibold">Serveur</h1>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Utensils className="h-4 w-4" />
            <span>Sur Place</span>
            {orderState.tableId && (
              <>
                <span>•</span>
                <span className="font-medium">Table {tables?.find(t => t.id === orderState.tableId)?.name}</span>
              </>
            )}
          </div>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher..."
            value={uiState.searchQuery}
            onChange={(e) => setUiState(prev => ({ ...prev, searchQuery: e.target.value }))}
            className="pl-10 h-9 text-base"
          />
        </div>
      </div>

      {/* Category Navigation */}
      <div className="flex-shrink-0 border-b bg-background">
        <ScrollArea className="w-full">
          <div className="flex gap-2 p-3">
            {categories?.map((category) => (
              <Button
                key={category.id}
                variant={uiState.selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setUiState(prev => ({ ...prev, selectedCategory: category.id }))}
                className="whitespace-nowrap h-8 text-xs"
                style={{
                  backgroundColor: uiState.selectedCategory === category.id ? category.color : undefined,
                  borderColor: category.color,
                  color: uiState.selectedCategory === category.id ? 'white' : category.color
                }}
              >
                {category.name}
              </Button>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Menu Items */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-3">
            {menuLoading ? (
              <div className="grid grid-cols-1 gap-3">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-20 bg-muted rounded-lg animate-pulse" />
                ))}
              </div>
            ) : (
              <MenuItemsGrid
                categories={categories}
                selectedCategory={uiState.selectedCategory}
                searchQuery={uiState.searchQuery}
                staffMenuItems={staffMenuItems}
                selectedItemForAddons={uiState.selectedItemForAddons}
                selectedItemSizes={uiState.selectedItemSizes}
                lastAddedItem={uiState.lastAddedItem}
                onAddItem={handleAddItem}
              />
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="flex-shrink-0 border-t bg-background/95 backdrop-blur p-3">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setUiState(prev => ({ ...prev, showCart: true }))}
            className="flex-1 h-12 relative"
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            Panier ({orderState.items.length})
            {orderState.items.length > 0 && (
              <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                {orderState.items.reduce((sum, item) => sum + item.quantity, 0)}
              </Badge>
            )}
          </Button>

          {orderState.items.length > 0 && orderState.tableId && (
            <Button
              onClick={handlePlaceOrder}
              disabled={isPlacingOrder}
              className="h-12 px-6"
            >
              {isPlacingOrder ? 'Envoi...' : 'Commander'}
            </Button>
          )}

          {orderState.items.length > 0 && (
            <div className="text-right">
              <div className="text-sm font-medium">{orderState.total.toFixed(2)} DA</div>
              <div className="text-xs text-muted-foreground">Total</div>
            </div>
          )}
        </div>
      </div>

      {/* Cart Sheet */}
      <Sheet open={uiState.showCart} onOpenChange={(open) => setUiState(prev => ({ ...prev, showCart: open }))}>
        <SheetContent side="right" className="w-full sm:max-w-md">
          <SheetHeader>
            <SheetTitle>Panier ({orderState.items.length})</SheetTitle>
          </SheetHeader>
          <div className="flex flex-col h-full">
            <ScrollArea className="flex-1 mt-4">
              {orderState.items.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">Votre panier est vide</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {orderState.items.map((item) => (
                    <Card key={item.id}>
                      <CardContent className="p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm truncate">{item.name}</h4>
                            <div className="text-xs text-muted-foreground mt-1">
                              {item.size !== 'default' && <span>Taille: {item.size} • </span>}
                              {item.price} DA
                            </div>
                            {item.addons && item.addons.length > 0 && (
                              <div className="text-xs text-muted-foreground mt-1">
                                Suppléments: {item.addons.map(addon => addon.name).join(', ')}
                              </div>
                            )}
                            {item.notes && (
                              <div className="text-xs text-muted-foreground mt-1">
                                Note: {item.notes}
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2 ml-3">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => dispatch({ type: 'DECREMENT_QUANTITY', payload: { itemId: item.id } })}
                              className="h-6 w-6 p-0"
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="text-sm font-medium w-6 text-center">{item.quantity}</span>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => dispatch({ type: 'INCREMENT_QUANTITY', payload: { itemId: item.id } })}
                              className="h-6 w-6 p-0"
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => dispatch({ type: 'REMOVE_ITEM', payload: { itemId: item.id } })}
                              className="h-6 w-6 p-0 text-destructive"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </ScrollArea>

            {orderState.items.length > 0 && (
              <div className="border-t pt-4 mt-4">
                <div className="flex justify-between items-center mb-4">
                  <span className="font-semibold">Total:</span>
                  <span className="font-semibold text-lg">{orderState.total.toFixed(2)} DA</span>
                </div>
                <Button
                  onClick={() => {
                    setUiState(prev => ({ ...prev, showCart: false, showOrderTypeSelector: true }));
                  }}
                  className="w-full"
                  disabled={orderState.items.length === 0}
                >
                  Continuer la commande
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Item Customization Panel (inline like NewOrderingInterface) */}
      {uiState.selectedItemForAddons && (
        <ItemCustomizationPanel
          categories={categories}
          uiState={uiState}
          setUiState={setUiState}
          dispatch={dispatch}
          getAddonKey={getAddonKey}
          getItemNoteKey={getItemNoteKey}
          onClose={() => {
            // Remove the most recent item and close panel
            if (orderState.items.length > 0) {
              const mostRecentItem = orderState.items[orderState.items.length - 1];
              dispatch({ type: 'REMOVE_ITEM', payload: { itemId: mostRecentItem.id } });
            }
            setUiState(prev => ({ ...prev, selectedItemForAddons: null }));
          }}
        />
      )}

      {/* Table Selection Dialog */}
      <TableSelectionDialog
        isOpen={!orderState.tableId}
        tables={tables}
        onTableSelect={(tableId) => dispatch({ type: 'SET_TABLE', payload: { tableId } })}
      />
    </div>
  );
}

// ItemCustomizationPanel Component (matches NewOrderingInterface)
interface ItemCustomizationPanelProps {
  categories: Category[] | undefined;
  uiState: UiState;
  setUiState: React.Dispatch<React.SetStateAction<UiState>>;
  dispatch: React.Dispatch<OrderAction>;
  getAddonKey: (itemId: string, size: string) => string;
  getItemNoteKey: (itemId: string, size: string) => string;
  onClose: () => void;
}

const ItemCustomizationPanel: React.FC<ItemCustomizationPanelProps> = ({
  categories,
  uiState,
  setUiState,
  dispatch,
  getAddonKey,
  getItemNoteKey,
  onClose
}) => {
  if (!uiState.selectedItemForAddons) return null;

  const [itemId, selectedSize] = uiState.selectedItemForAddons.split('-');

  // Find the item and category
  let item: MenuItem | undefined;
  let category: Category | undefined;

  for (const cat of categories || []) {
    const foundItem = cat.items?.find(i => i.id === itemId);
    if (foundItem) {
      item = foundItem;
      category = cat;
      break;
    }
  }

  if (!item || !category) return null;

  const addonKey = getAddonKey(itemId, selectedSize);
  const noteKey = getItemNoteKey(itemId, selectedSize);
  const currentAddons = uiState.selectedAddons[addonKey] || new Set();
  const currentNote = uiState.itemNotes[noteKey] || '';

  const toggleAddon = (addonId: string) => {
    const newAddons = new Set(currentAddons);
    if (newAddons.has(addonId)) {
      newAddons.delete(addonId);
    } else {
      newAddons.add(addonId);
    }
    setUiState(prev => ({
      ...prev,
      selectedAddons: { ...prev.selectedAddons, [addonKey]: newAddons }
    }));
  };

  const updateNote = (note: string) => {
    setUiState(prev => ({
      ...prev,
      itemNotes: { ...prev.itemNotes, [noteKey]: note }
    }));
  };

  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b bg-background">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h2 className="text-lg font-semibold">{item.name}</h2>
            <Badge variant="outline">{selectedSize === 'default' ? 'Standard' : selectedSize}</Badge>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {/* Add-ons */}
          {category.supplements && category.supplements.length > 0 && (
            <div>
              <h3 className="font-medium mb-3">Suppléments</h3>
              <div className="space-y-2">
                {category.supplements.map((addon) => {
                  const addonPrice = addon.prices?.[selectedSize] || addon.prices?.['default'] || 0;
                  const isSelected = currentAddons.has(addon.id);

                  return (
                    <div
                      key={addon.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        isSelected ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => toggleAddon(addon.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <span className="font-medium">{addon.name}</span>
                          <span className="text-sm text-muted-foreground ml-2">+{addonPrice} DA</span>
                        </div>
                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                          isSelected ? 'border-primary bg-primary' : 'border-border'
                        }`}>
                          {isSelected && <Check className="h-3 w-3 text-white" />}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Notes */}
          <div>
            <h3 className="font-medium mb-3">Instructions spéciales</h3>
            <Textarea
              placeholder="Ajouter des instructions..."
              value={currentNote}
              onChange={(e) => updateNote(e.target.value)}
              className="min-h-[80px] text-base"
            />
          </div>
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="flex-shrink-0 p-4 border-t bg-background">
        <Button
          onClick={() => setUiState(prev => ({ ...prev, selectedItemForAddons: null }))}
          className="w-full"
        >
          Confirmer
        </Button>
      </div>
    </div>
  );
};

// TableSelectionDialog Component
interface TableSelectionDialogProps {
  isOpen: boolean;
  tables: Table[] | undefined;
  onTableSelect: (tableId: string) => void;
}

const TableSelectionDialog: React.FC<TableSelectionDialogProps> = ({
  isOpen,
  tables,
  onTableSelect
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Sélectionner une table</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-3 gap-2 max-h-60 overflow-y-auto">
          {tables?.map((table) => (
            <Button
              key={table.id}
              variant="outline"
              onClick={() => onTableSelect(table.id)}
              className="h-16 flex flex-col"
            >
              <span className="font-medium">Table</span>
              <span className="text-lg">{table.name}</span>
            </Button>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};


