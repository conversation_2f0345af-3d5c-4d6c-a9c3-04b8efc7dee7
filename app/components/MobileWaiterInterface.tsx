"use client";

import React, { useState, useEffect, useCallback, useReducer, useMemo } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { useOrderV4 } from '@/lib/hooks/use-order-v4';
import { useTableDB } from '@/lib/hooks/useTableDB';
import { useStaffMenuV4 } from '@/lib/hooks/useStaffMenuV4';
import { useSupplements } from '@/lib/hooks/useSupplements';
import { useToast } from '@/components/ui/use-toast';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetHeader, Sheet<PERSON>it<PERSON>, SheetTrigger } from '@/components/ui/sheet';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent } from '@/components/ui/card';

// Icons
import {
  Search,
  ShoppingCart,
  Plus,
  Minus,
  X,
  Check,
  Utensils
} from 'lucide-react';

// Types
import type { MenuItem, Addon, Category } from '@/lib/db/v4-menu-service';
import type { OrderItem, OrderAddon, NewOrder } from '@/lib/types';
import type { OrderType } from '@/lib/types/order-types';
import type { PizzaQuarter } from '@/lib/types/pizza-types';
import { TableLayout as Table } from '@/lib/db/table-db';

// Order state interface
interface OrderState {
  _id: string;
  id: string;
  type: string;
  orderType: OrderType;
  status: string;
  tableId: string;
  items: OrderItem[];
  total: number;
  createdAt: string;
  updatedAt: string;
  schemaVersion: string;
  notes: string;
}

// UI state interface
interface UiState {
  selectedCategory: string;
  selectedItemForAddons: string | null;
  selectedItemSizes: {[key: string]: string};
  itemNotes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  searchQuery: string;
  showCart: boolean;
  lastAddedItem: string | null;
}

// Order actions
type OrderAction =
  | { type: 'INITIALIZE_ORDER' }
  | { type: 'ADD_ITEM', payload: { item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string } }
  | { type: 'UPDATE_ITEM', payload: { itemId: string, addons: OrderAddon[], notes: string } }
  | { type: 'ADD_CUSTOM_PIZZA', payload: { quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average' } }
  | { type: 'INCREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'DECREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'REMOVE_ITEM', payload: { itemId: string } }
  | { type: 'SET_TABLE', payload: { tableId: string } }
  | { type: 'SET_NOTES', payload: { notes: string } };

// Initial states
const initialOrderState: OrderState = {
  _id: "",
  id: "",
  type: "order_document",
  orderType: "dine-in",
  status: "pending",
  tableId: "",
  items: [],
  total: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  schemaVersion: "v4.0",
  notes: ""
};

const initialUiState: UiState = {
  selectedCategory: "",
  selectedItemForAddons: null,
  selectedItemSizes: {},
  itemNotes: {},
  selectedAddons: {},
  searchQuery: "",
  showCart: false,
  lastAddedItem: null
};

// Calculate total helper
const calculateTotal = (items: OrderItem[]): number => {
  return items.reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const addonsTotal = item.addons?.reduce((sum, addon) => sum + (addon.price * item.quantity), 0) || 0;
    return total + itemTotal + addonsTotal;
  }, 0);
};

// Order reducer
const orderReducer = (state: OrderState, action: OrderAction): OrderState => {
  switch (action.type) {
    case 'INITIALIZE_ORDER':
      return { ...initialOrderState };
      
    case 'ADD_ITEM': {
      const { item, size, addons, notes, categoryId } = action.payload;
      const price = item.prices[size] || Object.values(item.prices)[0] || 0;
      const uniqueId = `order_item_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: item.id,
        name: item.name,
        size: size,
        price: price,
        quantity: 1,
        addons: addons,
        notes: notes,
        categoryId: categoryId
      };

      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }

    case 'UPDATE_ITEM': {
      const { itemId, addons, notes } = action.payload;
      const updatedItems = state.items.map(item =>
        item.id === itemId
          ? { ...item, addons, notes }
          : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }

    case 'ADD_CUSTOM_PIZZA': {
      const { quarters, size, notes, categoryId, pricingMethod } = action.payload;
      const uniqueId = `custom_pizza_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      let price = 0;
      if (pricingMethod === 'max') {
        price = Math.max(...quarters.map(q => q.price));
      } else {
        price = quarters.reduce((sum, q) => sum + q.price, 0) / quarters.length;
      }
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: 'custom_pizza',
        name: `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`,
        size: size,
        price: price,
        quantity: 1,
        addons: [],
        notes: notes,
        categoryId: categoryId,
        compositeType: 'pizza_quarters',
        quarters: quarters
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'INCREMENT_QUANTITY': {
      const updatedItems = state.items.map(item =>
        item.id === action.payload.itemId
          ? { ...item, quantity: item.quantity + 1 }
          : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'DECREMENT_QUANTITY': {
      const updatedItems = state.items.map(item =>
        item.id === action.payload.itemId && item.quantity > 1
          ? { ...item, quantity: item.quantity - 1 }
          : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'REMOVE_ITEM': {
      const updatedItems = state.items.filter(item => item.id !== action.payload.itemId);
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'SET_ORDER_TYPE':
      return { ...state, orderType: action.payload.orderType };
      
    case 'SET_TABLE':
      return { ...state, tableId: action.payload.tableId };

    case 'SET_NOTES':
      return { ...state, notes: action.payload.notes };
      
    default:
      return state;
  }
};

// Helper functions
const requiresTable = (orderType: OrderType): boolean => orderType === 'dine-in';
const requiresCustomerInfo = (orderType: OrderType): boolean => orderType === 'delivery' || orderType === 'takeaway';

// Color utility functions (from NewOrderingInterface)
const getLightColor = (color: string): string => {
  // Convert hex to RGB
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // Make it lighter by blending with white
  const lightR = Math.round(r + (255 - r) * 0.8);
  const lightG = Math.round(g + (255 - g) * 0.8);
  const lightB = Math.round(b + (255 - b) * 0.8);

  return `rgb(${lightR}, ${lightG}, ${lightB})`;
};

// Color utility functions (from NewOrderingInterface)
const getLightColor = (color: string): string => {
  // Convert hex to RGB
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // Make it lighter by blending with white
  const lightR = Math.round(r + (255 - r) * 0.8);
  const lightG = Math.round(g + (255 - g) * 0.8);
  const lightB = Math.round(b + (255 - b) * 0.8);

  return `rgb(${lightR}, ${lightG}, ${lightB})`;
};

// MenuItemCard component (matches NewOrderingInterface)
interface MenuItemCardProps {
  item: MenuItem;
  categoryId: string;
  isSelected: boolean;
  selectedSize: string | undefined;
  lastAddedItem: string | null;
  onAddItem: (item: MenuItem, size: string) => void;
}

const MenuItemCard: React.FC<MenuItemCardProps> = ({
  item,
  categoryId,
  isSelected,
  selectedSize,
  lastAddedItem,
  onAddItem
}) => {
  const handleSizeButtonClick = useCallback((e: React.MouseEvent, size: string) => {
    e.stopPropagation();
    onAddItem(item, size);
  }, [item, onAddItem]);

  return (
    <Card className={`transition-all duration-200 ${isSelected ? "ring-2 ring-primary/40 bg-primary/5" : "hover:shadow-sm"}`}>
      <CardContent className="p-2">
        <div className="mb-2">
          <h3 className="font-semibold text-sm leading-tight">{item.name}</h3>
        </div>

        {/* Ultra-compact size buttons */}
        <div className="grid gap-1" style={{ gridTemplateColumns: `repeat(${Object.keys(item.prices).length}, 1fr)` }}>
          {Object.entries(item.prices).map(([size, price]) => {
            const isItemSizeSelected = isSelected && selectedSize === size;
            const isJustAdded = lastAddedItem === `${item.id}-${size}`;

            return (
              <Button
                key={size}
                size="sm"
                variant={isItemSizeSelected ? "default" : isJustAdded ? "secondary" : "outline"}
                onClick={(e) => handleSizeButtonClick(e, size)}
                className="h-8 px-2 text-xs font-medium hover:scale-[1.02] transition-transform flex flex-col items-center justify-center"
              >
                <span className="text-xs font-medium">
                  {size === 'default' ? 'Std' : size}
                </span>
                <span className="text-xs font-bold">
                  {price as number}
                </span>
                {isJustAdded && <Check className="h-2 w-2 mt-0.5 text-green-600" />}
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

// MenuItemsGrid component
interface MenuItemsGridProps {
  categories: Category[] | undefined;
  selectedCategory: string;
  searchQuery: string;
  staffMenuItems: MenuItem[] | undefined;
  selectedItemForAddons: string | null;
  selectedItemSizes: {[key: string]: string};
  lastAddedItem: string | null;
  onAddItem: (item: MenuItem, size: string) => void;
}

const MenuItemsGrid: React.FC<MenuItemsGridProps> = ({
  categories,
  selectedCategory,
  searchQuery,
  staffMenuItems,
  selectedItemForAddons,
  selectedItemSizes,
  lastAddedItem,
  onAddItem
}) => {
  const currentCategory = categories?.find(cat => cat.id === selectedCategory);

  if (!currentCategory) return null;

  // Filter items based on search
  const filteredItems = currentCategory.items?.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  // Add staff menu items if they match the search
  const staffItems = staffMenuItems?.filter(item =>
    searchQuery && (
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  ) || [];

  const allItems = [...filteredItems, ...staffItems];

  if (allItems.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-muted-foreground">
          <Utensils className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <h3 className="font-medium text-lg mb-2">
            {searchQuery ? 'Aucun article trouvé' : 'Aucun article disponible'}
          </h3>
          <p className="text-sm">
            {searchQuery
              ? 'Essayez un autre terme de recherche'
              : 'Cette catégorie ne contient pas d\'articles pour le moment'
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-2">
      {allItems.map((item) => {
        const selectedSize = selectedItemSizes[item.id];
        const isSelected = selectedSize && selectedItemForAddons === `${item.id}-${selectedSize}`;

        return (
          <MenuItemCard
            key={item.id}
            item={item}
            categoryId={currentCategory.id}
            isSelected={isSelected}
            selectedSize={selectedSize}
            lastAddedItem={lastAddedItem}
            onAddItem={onAddItem}
          />
        );
      })}
    </div>
  );
};

export default function MobileWaiterInterface() {
  // Hooks
  const { user } = useAuth();
  const { categories, isLoading: menuLoading, isReady: menuReady } = useMenuV4();
  const { tables, isLoading: tablesLoading, isReady: tablesReady } = useTableDB();
  const { createOrder, isLoading: ordersLoading } = useOrderV4();
  const { staffMenuItems } = useStaffMenuV4();
  const { toast } = useToast();

  // State
  const [orderState, dispatch] = useReducer(orderReducer, initialOrderState);
  const [uiState, setUiState] = useState<UiState>(initialUiState);
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);

  // Helper functions for addon/note keys (matches NewOrderingInterface)
  const getAddonKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);
  const getItemNoteKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);

  // Set initial category when categories load
  useEffect(() => {
    if (categories?.length > 0 && !uiState.selectedCategory) {
      setUiState(prev => ({ ...prev, selectedCategory: categories[0].id }));
    }
  }, [categories, uiState.selectedCategory]);

  // Handle adding item (matches NewOrderingInterface flow exactly)
  const handleAddItem = useCallback((item: MenuItem, size: string) => {
    const categoryId = uiState.selectedCategory;

    // Get current addons and notes for this item+size
    const addonKey = getAddonKey(item.id, size);
    const noteKey = getItemNoteKey(item.id, size);
    const selectedAddons = uiState.selectedAddons[addonKey] || new Set();
    const itemNotes = uiState.itemNotes[noteKey] || '';

    // Convert addons to OrderAddon format
    const category = categories?.find(cat => cat.id === categoryId);
    const addonObjects: OrderAddon[] = Array.from(selectedAddons).map(addonId => {
      const addon = category?.supplements?.find(s => s.id === addonId);
      return addon ? {
        id: addon.id,
        name: addon.name,
        price: addon.prices?.[size] || addon.prices?.['default'] || 0
      } : null;
    }).filter(Boolean) as OrderAddon[];

    // Add item to order
    dispatch({
      type: 'ADD_ITEM',
      payload: {
        item,
        size,
        addons: addonObjects,
        notes: itemNotes,
        categoryId: categoryId
      }
    });

    // Create signature for this specific item+size+addons combination
    const signature = `${item.id}-${size}-${Array.from(selectedAddons).sort().join(',')}-${itemNotes}`;
    setUiState(prev => ({ ...prev, lastAddedItem: signature }));

    // Clear the addon selection for this item after adding
    setUiState(prev => ({
      ...prev,
      selectedAddons: {
        ...prev.selectedAddons,
        [getAddonKey(item.id, size)]: new Set()
      },
      itemNotes: {
        ...prev.itemNotes,
        [getItemNoteKey(item.id, size)]: ''
      }
    }));

    // Show success toast
    toast({
      title: "Article ajouté",
      description: `${item.name} (${size === 'default' ? 'Standard' : size}) ajouté au panier`,
      duration: 2000,
    });

    // 🎯 Open customization panel after adding item to allow addon/notes customization
    setUiState(prev => ({
      ...prev,
      selectedItemForAddons: `${item.id}-${size}`,
      selectedItemSizes: { ...prev.selectedItemSizes, [item.id]: size }
    }));
  }, [uiState.selectedAddons, uiState.itemNotes, uiState.selectedCategory, getAddonKey, getItemNoteKey, categories, dispatch, toast]);

  // Place order function (simplified for dine-in only)
  const handlePlaceOrder = useCallback(async () => {
    if (!orderState.items?.length) return;
    if (!orderState.tableId) return;

    setIsPlacingOrder(true);

    try {
      const newOrder: NewOrder = {
        tableId: orderState.tableId,
        orderType: 'dine-in',
        status: 'pending',
        items: orderState.items.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
        total: orderState.total,
        notes: orderState.notes,
        paymentStatus: 'unpaid',
        createdBy: user?.name || (user as any)?.username || 'unknown',
        createdByName: user?.name || 'Personnel Inconnu'
      };

      await createOrder(newOrder);

      toast({
        title: "Commande créée",
        description: "La commande a été créée avec succès.",
      });

      // Reset order
      dispatch({ type: 'INITIALIZE_ORDER' });
      setUiState(initialUiState);

    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: "Erreur",
        description: "Impossible de créer la commande. Veuillez réessayer.",
        variant: "destructive"
      });
    } finally {
      setIsPlacingOrder(false);
    }
  }, [orderState, user, createOrder, toast]);

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Mobile Header */}
      <div className="flex-shrink-0 p-3 border-b bg-background/95 backdrop-blur">
        <div className="flex items-center justify-between mb-3">
          <h1 className="text-lg font-semibold">Serveur</h1>
          <div className="flex items-center gap-2 text-sm">
            <Utensils className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">Sur Place</span>
            {orderState.tableId && (
              <>
                <span className="text-muted-foreground">•</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => dispatch({ type: 'SET_TABLE', payload: { tableId: '' } })}
                  className="h-auto p-1 font-medium text-primary hover:text-primary/80"
                >
                  Table {tables?.find(t => t.id === orderState.tableId)?.name}
                </Button>
              </>
            )}
          </div>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher..."
            value={uiState.searchQuery}
            onChange={(e) => setUiState(prev => ({ ...prev, searchQuery: e.target.value }))}
            className="pl-10 h-9 text-base"
          />
        </div>
      </div>

      {/* Category Navigation - Ultra Compact */}
      <div className="flex-shrink-0 border-b bg-background">
        <ScrollArea className="w-full">
          <div className="flex gap-2 p-2">
            {menuLoading ? (
              // Loading skeleton for categories
              [...Array(4)].map((_, i) => (
                <div key={i} className="h-8 w-20 bg-muted rounded animate-pulse flex-shrink-0" />
              ))
            ) : (
              categories?.map((category) => (
                <Button
                  key={category.id}
                  variant={uiState.selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setUiState(prev => ({ ...prev, selectedCategory: category.id }))}
                  className="whitespace-nowrap h-8 px-3 text-xs font-medium flex-shrink-0 transition-all"
                  style={{
                    backgroundColor: uiState.selectedCategory === category.id ? category.color : undefined,
                    borderColor: category.color,
                    color: uiState.selectedCategory === category.id ? 'white' : category.color
                  }}
                >
                  {category.name}
                  <span className="ml-1 text-xs opacity-70">
                    {category.items?.length || 0}
                  </span>
                </Button>
              ))
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Menu Items - Ultra Compact Grid */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-2">
            {menuLoading ? (
              <div className="grid grid-cols-1 gap-2">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="h-16 bg-muted rounded animate-pulse" />
                ))}
              </div>
            ) : (
              <MenuItemsGrid
                categories={categories}
                selectedCategory={uiState.selectedCategory}
                searchQuery={uiState.searchQuery}
                staffMenuItems={staffMenuItems}
                selectedItemForAddons={uiState.selectedItemForAddons}
                selectedItemSizes={uiState.selectedItemSizes}
                lastAddedItem={uiState.lastAddedItem}
                onAddItem={handleAddItem}
              />
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Mobile Bottom Navigation - Ultra Compact */}
      <div className="flex-shrink-0 border-t bg-background/95 backdrop-blur">
        {orderState.items.length > 0 ? (
          <div className="p-2">
            {/* Compact Order Summary */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2 text-sm">
                <span className="font-medium">
                  {orderState.items.reduce((sum, item) => sum + item.quantity, 0)} articles
                </span>
                {orderState.tableId && (
                  <>
                    <span className="text-muted-foreground">•</span>
                    <span className="text-muted-foreground text-xs">
                      Table {tables?.find(t => t.id === orderState.tableId)?.name}
                    </span>
                  </>
                )}
              </div>
              <div className="font-bold text-base text-primary">
                {orderState.total.toFixed(0)} DA
              </div>
            </div>

            {/* Compact Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setUiState(prev => ({ ...prev, showCart: true }))}
                className="flex-1 h-9 text-sm"
              >
                <ShoppingCart className="h-3 w-3 mr-1" />
                Panier
              </Button>

              {orderState.tableId ? (
                <Button
                  onClick={handlePlaceOrder}
                  disabled={isPlacingOrder}
                  className="flex-1 h-9 text-sm font-semibold"
                >
                  {isPlacingOrder ? 'Envoi...' : 'Commander'}
                </Button>
              ) : (
                <Button
                  variant="secondary"
                  disabled
                  className="flex-1 h-9 text-sm"
                >
                  Sélectionner table
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="p-3 text-center">
            <div className="text-xs text-muted-foreground">
              Ajoutez des articles pour commencer
            </div>
          </div>
        )}
      </div>

      {/* Cart Sheet - Enhanced Mobile UX */}
      <Sheet open={uiState.showCart} onOpenChange={(open) => setUiState(prev => ({ ...prev, showCart: open }))}>
        <SheetContent side="right" className="w-full sm:max-w-md">
          <SheetHeader className="pb-4">
            <SheetTitle className="text-xl">
              Panier ({orderState.items.reduce((sum, item) => sum + item.quantity, 0)})
            </SheetTitle>
          </SheetHeader>

          <div className="flex flex-col h-full">
            <ScrollArea className="flex-1">
              {orderState.items.length === 0 ? (
                <div className="text-center py-12">
                  <ShoppingCart className="h-16 w-16 mx-auto text-muted-foreground mb-4 opacity-50" />
                  <h3 className="font-medium text-lg mb-2">Votre panier est vide</h3>
                  <p className="text-muted-foreground text-sm">
                    Ajoutez des articles depuis le menu
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {orderState.items.map((item) => (
                    <Card
                      key={item.id}
                      className="border-l-4 border-l-primary/20 cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => {
                        // Open customization for this item
                        setUiState(prev => ({
                          ...prev,
                          selectedItemForAddons: `${item.menuItemId}-${item.size}`,
                          selectedItemSizes: { ...prev.selectedItemSizes, [item.menuItemId]: item.size },
                          showCart: false
                        }));
                      }}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <h4 className="font-semibold text-base truncate">{item.name}</h4>
                              <span className="text-xs text-muted-foreground">Tap to edit</span>
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              {item.size !== 'default' && <span className="font-medium">Taille: {item.size} • </span>}
                              <span className="font-medium">{item.price} DA</span>
                            </div>
                            {item.addons && item.addons.length > 0 && (
                              <div className="text-sm text-muted-foreground mt-2">
                                <span className="font-medium">Suppléments:</span>
                                <div className="mt-1">
                                  {item.addons.map(addon => (
                                    <div key={addon.id} className="flex justify-between">
                                      <span>+ {addon.name}</span>
                                      <span>+{addon.price} DA</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                            {item.notes && (
                              <div className="text-sm text-muted-foreground mt-2 p-2 bg-muted/50 rounded">
                                <span className="font-medium">Note:</span> {item.notes}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Quantity controls - Mobile optimized */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                dispatch({ type: 'DECREMENT_QUANTITY', payload: { itemId: item.id } });
                              }}
                              className="h-8 w-8 p-0"
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                            <span className="text-lg font-semibold w-8 text-center">{item.quantity}</span>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                dispatch({ type: 'INCREMENT_QUANTITY', payload: { itemId: item.id } });
                              }}
                              className="h-8 w-8 p-0"
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>

                          <div className="flex items-center gap-3">
                            <span className="font-bold text-lg">
                              {((item.price + (item.addons?.reduce((sum, addon) => sum + addon.price, 0) || 0)) * item.quantity).toFixed(2)} DA
                            </span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                dispatch({ type: 'REMOVE_ITEM', payload: { itemId: item.id } });
                              }}
                              className="h-8 w-8 p-0 text-destructive hover:bg-destructive/10"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </ScrollArea>

            {orderState.items.length > 0 && (
              <div className="border-t pt-4 mt-4 space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold">Total:</span>
                  <span className="text-2xl font-bold text-primary">{orderState.total.toFixed(2)} DA</span>
                </div>

                {orderState.tableId && (
                  <Button
                    onClick={handlePlaceOrder}
                    disabled={isPlacingOrder}
                    className="w-full h-12 text-lg font-semibold"
                  >
                    {isPlacingOrder ? 'Envoi en cours...' : 'Passer la commande'}
                  </Button>
                )}

                {!orderState.tableId && (
                  <div className="text-center text-sm text-muted-foreground">
                    Sélectionnez une table pour continuer
                  </div>
                )}
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Item Customization Panel (matches NewOrderingInterface exactly) */}
      {uiState.selectedItemForAddons && (() => {
        const [itemId, selectedSize] = uiState.selectedItemForAddons.split('-');

        // Find the item and category
        let item: MenuItem | undefined;
        let category: Category | undefined;

        for (const cat of categories || []) {
          const foundItem = cat.items?.find(i => i.id === itemId);
          if (foundItem) {
            item = foundItem;
            category = cat;
            break;
          }
        }

        if (!item || !category) return null;

        return (
          <ItemCustomizationPanel
            category={category}
            item={item}
            selectedSize={selectedSize}
            selectedAddons={uiState.selectedAddons}
            itemNotes={uiState.itemNotes}
            getAddonKey={getAddonKey}
            getItemNoteKey={getItemNoteKey}
            isAddonSelected={(itemId: string, size: string, addonId: string) => {
              const addonKey = getAddonKey(itemId, size);
              return uiState.selectedAddons[addonKey]?.has(addonId) || false;
            }}
            toggleAddonSelection={(itemId: string, size: string, addonId: string) => {
              const addonKey = getAddonKey(itemId, size);
              const currentAddons = uiState.selectedAddons[addonKey] || new Set();
              const newAddons = new Set(currentAddons);

              if (newAddons.has(addonId)) {
                newAddons.delete(addonId);
              } else {
                newAddons.add(addonId);
              }

              setUiState(prev => ({
                ...prev,
                selectedAddons: { ...prev.selectedAddons, [addonKey]: newAddons }
              }));
            }}
            updateItemNote={(itemId: string, size: string, note: string) => {
              const noteKey = getItemNoteKey(itemId, size);
              setUiState(prev => ({
                ...prev,
                itemNotes: { ...prev.itemNotes, [noteKey]: note }
              }));
            }}
            finalizeItem={() => {
              setUiState(prev => ({ ...prev, selectedItemForAddons: null }));
            }}
            categories={categories || []}
            onCustomPizzaConfirm={(quarters, size, notes, categoryId, pricingMethod) => {
              dispatch({
                type: 'ADD_CUSTOM_PIZZA',
                payload: { quarters, size, notes, categoryId, pricingMethod }
              });
              setUiState(prev => ({ ...prev, selectedItemForAddons: null }));
              toast({
                title: "🍕 Pizza personnalisée ajoutée",
                description: `Pizza avec ${quarters.map(q => q.name).join(' + ')} ajoutée à la commande`,
              });
            }}
            onClose={() => {
              // Remove the most recent item and close panel
              if (orderState.items.length > 0) {
                const mostRecentItem = orderState.items[orderState.items.length - 1];
                dispatch({ type: 'REMOVE_ITEM', payload: { itemId: mostRecentItem.id } });
              }
              setUiState(prev => ({ ...prev, selectedItemForAddons: null }));
            }}
          />
        );
      })()}

      {/* Floating Cart Button - Compact */}
      {orderState.items.length > 0 && !uiState.showCart && !uiState.selectedItemForAddons && (
        <div className="fixed bottom-16 right-3 z-40">
          <Button
            onClick={() => setUiState(prev => ({ ...prev, showCart: true }))}
            className="h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 relative"
          >
            <ShoppingCart className="h-4 w-4" />
            <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs font-bold">
              {orderState.items.reduce((sum, item) => sum + item.quantity, 0)}
            </Badge>
          </Button>
        </div>
      )}

      {/* Table Selection Dialog */}
      <TableSelectionDialog
        isOpen={!orderState.tableId}
        tables={tables}
        isLoading={tablesLoading}
        onTableSelect={(tableId) => dispatch({ type: 'SET_TABLE', payload: { tableId } })}
      />
    </div>
  );
}

// ItemCustomizationPanel Component (matches NewOrderingInterface exactly)
interface ItemCustomizationPanelProps {
  category: any;
  item: MenuItem;
  selectedSize: string;
  selectedAddons: {[key: string]: Set<string>};
  itemNotes: {[key: string]: string};
  getAddonKey: (itemId: string, size: string) => string;
  getItemNoteKey: (itemId: string, size: string) => string;
  isAddonSelected: (itemId: string, size: string, addonId: string) => boolean;
  toggleAddonSelection: (itemId: string, size: string, addonId: string) => void;
  updateItemNote: (itemId: string, size: string, note: string) => void;
  finalizeItem: () => void;
  categories: any[];
  onCustomPizzaConfirm?: (quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average') => void;
  onClose?: () => void;
}

const ItemCustomizationPanel: React.FC<ItemCustomizationPanelProps> = React.memo(({
  category,
  item,
  selectedSize,
  selectedAddons,
  itemNotes,
  getAddonKey,
  getItemNoteKey,
  isAddonSelected,
  toggleAddonSelection,
  updateItemNote,
  finalizeItem,
  categories,
  onCustomPizzaConfirm,
  onClose
}) => {
  // Get the color for this item from its category
  const itemColor = item.color || category.color || '#e0e0e0';
  const lightColor = getLightColor(itemColor);

  // Get supplements from the category-specific supplements hook
  const { supplements } = useSupplements(category.id);

  // Filter to get only active supplements for this category
  const activeSupplements = supplements.filter(supplement => supplement.isActive !== false);

  // State for pizza customization
  const [showPartitionSelection, setShowPartitionSelection] = useState(true);
  const [pizzaPartition, setPizzaPartition] = useState<'half' | 'quarter' | null>(null);
  const [pizzaSelections, setPizzaSelections] = useState<{
    half: { left: MenuItem | null, right: MenuItem | null };
    quarter: { topLeft: MenuItem | null, topRight: MenuItem | null, bottomLeft: MenuItem | null, bottomRight: MenuItem | null };
  }>({
    half: { left: null, right: null },
    quarter: { topLeft: null, topRight: null, bottomLeft: null, bottomRight: null }
  });

  // Get current item note
  const currentNote = itemNotes[getItemNoteKey(item.id, selectedSize)] || '';

  // Handle note input change
  const handleItemNote = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateItemNote(item.id, selectedSize, e.target.value);
  };

  // Handle panel close - should remove the last added item
  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      finalizeItem();
    }
  };

  // 🍕 Handle pizza selection for different partitions
  const handlePizzaSelect = (position: string, selectedPizza: MenuItem | null) => {
    if (!pizzaPartition) return;
    setPizzaSelections(prev => ({
      ...prev,
      [pizzaPartition]: {
        ...prev[pizzaPartition],
        [position]: selectedPizza
      }
    }));
  };

  // 🍕 Get current pizzas based on partition type
  const getCurrentPizzas = (): MenuItem[] => {
    if (!pizzaPartition) return [];

    const selections = pizzaSelections[pizzaPartition];
    return Object.values(selections).filter(Boolean) as MenuItem[];
  };

  // 🍕 Calculate custom pizza price
  const calculateCustomPizzaPrice = () => {
    const validPizzas = getCurrentPizzas();
    if (validPizzas.length === 0) return 0;

    const prices = validPizzas.map(pizza => pizza.prices[selectedSize] || Object.values(pizza.prices)[0] || 0);

    if (category.quarterPricingMethod === 'average') {
      return prices.reduce((sum, price) => sum + price, 0) / prices.length;
    } else {
      return Math.max(...prices); // max pricing (default)
    }
  };

  // 🍕 Generate custom pizza name
  const generateCustomPizzaName = () => {
    const validPizzas = getCurrentPizzas();
    if (validPizzas.length === 0) return "Pizza Personnalisée";
    if (validPizzas.length === 1) return validPizzas[0].name;

    const uniqueNames = [...new Set(validPizzas.map(p => p.name))];
    if (uniqueNames.length === 1) return uniqueNames[0];
    if (uniqueNames.length === 2) return `${uniqueNames[0]} / ${uniqueNames[1]}`;

    return `Pizza Mixte (${uniqueNames.length} types)`;
  };

  // 🍕 Confirm custom pizza creation
  const handleConfirmCustomPizza = () => {
    const validPizzas = getCurrentPizzas();
    if (validPizzas.length === 0) return;

    // Convert to PizzaQuarter format - create quarters based on partition type
    let pizzaQuarterData: PizzaQuarter[];

    if (pizzaPartition === 'half') {
      const { left, right } = pizzaSelections.half;
      pizzaQuarterData = [
        ...(left ? [
          { position: 'topLeft', name: left.name, price: left.prices[selectedSize] || Object.values(left.prices)[0] || 0 },
          { position: 'bottomLeft', name: left.name, price: left.prices[selectedSize] || Object.values(left.prices)[0] || 0 }
        ] : []),
        ...(right ? [
          { position: 'topRight', name: right.name, price: right.prices[selectedSize] || Object.values(right.prices)[0] || 0 },
          { position: 'bottomRight', name: right.name, price: right.prices[selectedSize] || Object.values(right.prices)[0] || 0 }
        ] : [])
      ];
    } else {
      const { topLeft, topRight, bottomLeft, bottomRight } = pizzaSelections.quarter;
      pizzaQuarterData = [
        ...(topLeft ? [{ position: 'topLeft', name: topLeft.name, price: topLeft.prices[selectedSize] || Object.values(topLeft.prices)[0] || 0 }] : []),
        ...(topRight ? [{ position: 'topRight', name: topRight.name, price: topRight.prices[selectedSize] || Object.values(topRight.prices)[0] || 0 }] : []),
        ...(bottomLeft ? [{ position: 'bottomLeft', name: bottomLeft.name, price: bottomLeft.prices[selectedSize] || Object.values(bottomLeft.prices)[0] || 0 }] : []),
        ...(bottomRight ? [{ position: 'bottomRight', name: bottomRight.name, price: bottomRight.prices[selectedSize] || Object.values(bottomRight.prices)[0] || 0 }] : [])
      ];
    }

    // Call the parent callback to add to order
    if (onCustomPizzaConfirm) {
      onCustomPizzaConfirm(
        pizzaQuarterData,
        selectedSize,
        itemNotes[getItemNoteKey(item.id, selectedSize)] || '',
        category.id,
        category.quarterPricingMethod || 'max'
      );
    }

    // Reset state
    setShowPartitionSelection(true);
    setPizzaPartition(null);
    setPizzaSelections({
      half: { left: null, right: null },
      quarter: { topLeft: null, topRight: null, bottomLeft: null, bottomRight: null }
    });
  };

  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b bg-background">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-semibold">{item.name}</h2>
            <Badge variant="secondary" className="text-sm">
              {selectedSize === 'default' ? 'Standard' : selectedSize}
            </Badge>
          </div>
          <Button variant="ghost" size="sm" onClick={handleClose} className="h-10 w-10">
            <X className="h-5 w-5" />
          </Button>
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          Personnalisez votre commande
        </p>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {/* Pizza Customization for Pizza Categories */}
          {category.allowCustomPizza && (
            <div>
              <h3 className="font-semibold text-lg mb-4">🍕 Pizza Personnalisée</h3>

              {showPartitionSelection ? (
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    Choisissez comment diviser votre pizza:
                  </p>
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setPizzaPartition('half');
                        setShowPartitionSelection(false);
                      }}
                      className="h-16 flex flex-col items-center justify-center"
                    >
                      <span className="font-medium">Moitié-Moitié</span>
                      <span className="text-xs text-muted-foreground">2 saveurs</span>
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setPizzaPartition('quarter');
                        setShowPartitionSelection(false);
                      }}
                      className="h-16 flex flex-col items-center justify-center"
                    >
                      <span className="font-medium">Quart-Quart</span>
                      <span className="text-xs text-muted-foreground">4 saveurs</span>
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">
                      {pizzaPartition === 'half' ? 'Sélection Moitié-Moitié' : 'Sélection Quart-Quart'}
                    </h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setShowPartitionSelection(true);
                        setPizzaPartition(null);
                      }}
                    >
                      Changer
                    </Button>
                  </div>

                  {/* Pizza Selection Grid */}
                  <div className="grid gap-3">
                    {pizzaPartition === 'half' ? (
                      <>
                        <div>
                          <label className="text-sm font-medium">Moitié Gauche:</label>
                          <div className="mt-2 grid grid-cols-2 gap-2">
                            {category.items?.map((pizza: MenuItem) => (
                              <Button
                                key={pizza.id}
                                variant={pizzaSelections.half.left?.id === pizza.id ? "default" : "outline"}
                                size="sm"
                                onClick={() => handlePizzaSelect('left', pizza)}
                                className="text-xs h-8"
                              >
                                {pizza.name}
                              </Button>
                            ))}
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Moitié Droite:</label>
                          <div className="mt-2 grid grid-cols-2 gap-2">
                            {category.items?.map((pizza: MenuItem) => (
                              <Button
                                key={pizza.id}
                                variant={pizzaSelections.half.right?.id === pizza.id ? "default" : "outline"}
                                size="sm"
                                onClick={() => handlePizzaSelect('right', pizza)}
                                className="text-xs h-8"
                              >
                                {pizza.name}
                              </Button>
                            ))}
                          </div>
                        </div>
                      </>
                    ) : (
                      <>
                        {['topLeft', 'topRight', 'bottomLeft', 'bottomRight'].map((position) => (
                          <div key={position}>
                            <label className="text-sm font-medium">
                              {position === 'topLeft' ? 'Haut Gauche' :
                               position === 'topRight' ? 'Haut Droite' :
                               position === 'bottomLeft' ? 'Bas Gauche' : 'Bas Droite'}:
                            </label>
                            <div className="mt-2 grid grid-cols-2 gap-2">
                              {category.items?.map((pizza: MenuItem) => (
                                <Button
                                  key={pizza.id}
                                  variant={pizzaSelections.quarter[position as keyof typeof pizzaSelections.quarter]?.id === pizza.id ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => handlePizzaSelect(position, pizza)}
                                  className="text-xs h-8"
                                >
                                  {pizza.name}
                                </Button>
                              ))}
                            </div>
                          </div>
                        ))}
                      </>
                    )}
                  </div>

                  {/* Pizza Summary */}
                  {getCurrentPizzas().length > 0 && (
                    <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="font-medium text-orange-800 mb-1">{generateCustomPizzaName()}</div>
                      <div className="flex justify-between text-orange-600 text-sm">
                        <span>Prix ({category.quarterPricingMethod === 'average' ? 'Moyen' : 'Maximum'}):</span>
                        <span className="font-semibold">{calculateCustomPizzaPrice().toFixed(0)} DA</span>
                      </div>
                    </div>
                  )}

                  {getCurrentPizzas().length > 0 && (
                    <Button
                      onClick={handleConfirmCustomPizza}
                      className="w-full"
                    >
                      Confirmer Pizza Personnalisée
                    </Button>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Supplements Section */}
          {activeSupplements.length > 0 && (
            <div>
              <h3 className="font-semibold text-lg mb-4">Suppléments disponibles</h3>
              <div className="space-y-3">
                {activeSupplements.map((supplement) => {
                  const supplementPrice = supplement.prices?.[selectedSize] || supplement.prices?.['default'] || 0;
                  const isSelected = isAddonSelected(item.id, selectedSize, supplement.id);

                  return (
                    <div
                      key={supplement.id}
                      className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                        isSelected
                          ? 'border-primary bg-primary/10 shadow-md'
                          : 'border-border hover:border-primary/50 hover:shadow-sm'
                      }`}
                      onClick={() => toggleAddonSelection(item.id, selectedSize, supplement.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="font-semibold text-base">{supplement.name}</div>
                          <div className="text-sm text-muted-foreground mt-1">
                            Supplément: +{supplementPrice} DA
                          </div>
                        </div>
                        <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors ${
                          isSelected
                            ? 'border-primary bg-primary'
                            : 'border-muted-foreground/30'
                        }`}>
                          {isSelected && <Check className="h-4 w-4 text-white" />}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Notes Section */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Instructions spéciales</h3>
            <Input
              placeholder="Ajoutez des instructions particulières pour cet article..."
              value={currentNote}
              onChange={handleItemNote}
              className="text-base"
            />
            <div className="text-xs text-muted-foreground mt-2">
              Exemple: Sans oignons, bien cuit, sauce à part...
            </div>
          </div>
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="flex-shrink-0 p-4 border-t bg-background">
        <Button
          onClick={finalizeItem}
          className="w-full h-12 text-lg font-semibold"
        >
          Confirmer les modifications
        </Button>
      </div>
    </div>
  );
}, (prevProps: ItemCustomizationPanelProps, nextProps: ItemCustomizationPanelProps) => {
  // Only re-render if these specific props change
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.selectedSize === nextProps.selectedSize &&
    prevProps.itemNotes[prevProps.getItemNoteKey(prevProps.item.id, prevProps.selectedSize)] ===
      nextProps.itemNotes[nextProps.getItemNoteKey(nextProps.item.id, nextProps.selectedSize)] &&
    JSON.stringify(Array.from(prevProps.selectedAddons[prevProps.getAddonKey(prevProps.item.id, prevProps.selectedSize)] || new Set())) ===
      JSON.stringify(Array.from(nextProps.selectedAddons[nextProps.getAddonKey(nextProps.item.id, nextProps.selectedSize)] || new Set()))
  );
});

  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b bg-background">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-semibold">{item.name}</h2>
            <Badge variant="secondary" className="text-sm">
              {selectedSize === 'default' ? 'Standard' : selectedSize}
            </Badge>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-10 w-10">
            <X className="h-5 w-5" />
          </Button>
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          Personnalisez votre commande
        </p>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {/* Add-ons */}
          {category.supplements && category.supplements.length > 0 && (
            <div>
              <h3 className="font-semibold text-lg mb-4">Suppléments disponibles</h3>
              <div className="space-y-3">
                {category.supplements.map((addon) => {
                  const addonPrice = addon.prices?.[selectedSize] || addon.prices?.['default'] || 0;
                  const isSelected = currentAddons.has(addon.id);

                  return (
                    <div
                      key={addon.id}
                      className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                        isSelected
                          ? 'border-primary bg-primary/10 shadow-md'
                          : 'border-border hover:border-primary/50 hover:shadow-sm'
                      }`}
                      onClick={() => toggleAddon(addon.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="font-semibold text-base">{addon.name}</div>
                          <div className="text-sm text-muted-foreground mt-1">
                            Supplément: +{addonPrice} DA
                          </div>
                        </div>
                        <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors ${
                          isSelected
                            ? 'border-primary bg-primary'
                            : 'border-muted-foreground/30'
                        }`}>
                          {isSelected && <Check className="h-4 w-4 text-white" />}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {currentAddons.size > 0 && (
                <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                  <div className="text-sm font-medium">
                    Suppléments sélectionnés: +{Array.from(currentAddons).reduce((total, addonId) => {
                      const addon = category.supplements?.find(s => s.id === addonId);
                      return total + (addon?.prices?.[selectedSize] || addon?.prices?.['default'] || 0);
                    }, 0)} DA
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Notes */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Instructions spéciales</h3>
            <Textarea
              placeholder="Ajoutez des instructions particulières pour cet article..."
              value={currentNote}
              onChange={(e) => updateNote(e.target.value)}
              className="min-h-[100px] text-base resize-none"
            />
            <div className="text-xs text-muted-foreground mt-2">
              Exemple: Sans oignons, bien cuit, sauce à part...
            </div>
          </div>
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="flex-shrink-0 p-4 border-t bg-background">
        <Button
          onClick={() => setUiState(prev => ({ ...prev, selectedItemForAddons: null }))}
          className="w-full h-12 text-lg font-semibold"
        >
          Confirmer les modifications
        </Button>
      </div>
    </div>
  );
};

// TableSelectionDialog Component (Enhanced Mobile UX)
interface TableSelectionDialogProps {
  isOpen: boolean;
  tables: Table[] | undefined;
  isLoading: boolean;
  onTableSelect: (tableId: string) => void;
}

const TableSelectionDialog: React.FC<TableSelectionDialogProps> = ({
  isOpen,
  tables,
  isLoading,
  onTableSelect
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b bg-background">
        <div className="text-center">
          <h1 className="text-xl font-semibold">Sélectionner une table</h1>
          <p className="text-sm text-muted-foreground mt-1">
            Choisissez une table pour commencer la commande
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-4">
            {isLoading ? (
              <div className="grid grid-cols-2 gap-3">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="h-20 bg-muted rounded-lg animate-pulse" />
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-3">
                {tables?.map((table) => (
                  <Button
                    key={table.id}
                    variant="outline"
                    onClick={() => onTableSelect(table.id)}
                    className="h-20 flex flex-col gap-1 text-center hover:bg-primary hover:text-primary-foreground transition-colors"
                  >
                    <div className="text-sm font-medium text-muted-foreground">Table</div>
                    <div className="text-2xl font-bold">{table.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {table.seats} places
                    </div>
                  </Button>
                ))}
              </div>
            )}

            {!isLoading && (!tables || tables.length === 0) && (
              <div className="text-center py-12">
                <div className="text-muted-foreground">
                  <Utensils className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Aucune table disponible</p>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};


