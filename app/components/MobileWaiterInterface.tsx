"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useReducer, useMemo } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { useOrderV4 } from '@/lib/hooks/use-order-v4';
import { useTableDB } from '@/lib/hooks/useTableDB';
import { useStaffMenuV4 } from '@/lib/hooks/useStaffMenuV4';
import { useToast } from '@/components/ui/use-toast';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent } from '@/components/ui/card';

// Icons
import {
  Search,
  ShoppingCart,
  Plus,
  Minus,
  X,
  Check,
  Utensils
} from 'lucide-react';

// Types
import type { MenuItem, Addon, Category } from '@/lib/db/v4-menu-service';
import type { OrderItem, OrderAddon, NewOrder } from '@/lib/types';
import type { OrderType } from '@/lib/types/order-types';
import type { PizzaQuarter } from '@/lib/types/pizza-types';
import { TableLayout as Table } from '@/lib/db/table-db';

// Order state interface
interface OrderState {
  _id: string;
  id: string;
  type: string;
  orderType: OrderType;
  status: string;
  tableId: string;
  items: OrderItem[];
  total: number;
  createdAt: string;
  updatedAt: string;
  schemaVersion: string;
  notes: string;
}

// UI state interface
interface UiState {
  selectedCategory: string;
  selectedItemForAddons: string | null;
  selectedItemSizes: {[key: string]: string};
  itemNotes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  searchQuery: string;
  showCart: boolean;
  lastAddedItem: string | null;
}

// Order actions
type OrderAction =
  | { type: 'INITIALIZE_ORDER' }
  | { type: 'ADD_ITEM', payload: { item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string } }
  | { type: 'UPDATE_ITEM', payload: { itemId: string, addons: OrderAddon[], notes: string } }
  | { type: 'ADD_CUSTOM_PIZZA', payload: { quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average' } }
  | { type: 'INCREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'DECREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'REMOVE_ITEM', payload: { itemId: string } }
  | { type: 'SET_TABLE', payload: { tableId: string } }
  | { type: 'SET_NOTES', payload: { notes: string } };

// Initial states
const initialOrderState: OrderState = {
  _id: "",
  id: "",
  type: "order_document",
  orderType: "dine-in",
  status: "pending",
  tableId: "",
  items: [],
  total: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  schemaVersion: "v4.0",
  notes: ""
};

const initialUiState: UiState = {
  selectedCategory: "",
  selectedItemForAddons: null,
  selectedItemSizes: {},
  itemNotes: {},
  selectedAddons: {},
  searchQuery: "",
  showCart: false,
  lastAddedItem: null
};

// Calculate total helper
const calculateTotal = (items: OrderItem[]): number => {
  return items.reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const addonsTotal = item.addons?.reduce((sum, addon) => sum + (addon.price * item.quantity), 0) || 0;
    return total + itemTotal + addonsTotal;
  }, 0);
};

// Order reducer
const orderReducer = (state: OrderState, action: OrderAction): OrderState => {
  switch (action.type) {
    case 'INITIALIZE_ORDER':
      return { ...initialOrderState };
      
    case 'ADD_ITEM': {
      const { item, size, addons, notes, categoryId } = action.payload;
      const price = item.prices[size] || Object.values(item.prices)[0] || 0;
      const uniqueId = `order_item_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: item.id,
        name: item.name,
        size: size,
        price: price,
        quantity: 1,
        addons: addons,
        notes: notes,
        categoryId: categoryId
      };

      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }

    case 'UPDATE_ITEM': {
      const { itemId, addons, notes } = action.payload;
      const updatedItems = state.items.map(item =>
        item.id === itemId
          ? { ...item, addons, notes }
          : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }

    case 'ADD_CUSTOM_PIZZA': {
      const { quarters, size, notes, categoryId, pricingMethod } = action.payload;
      const uniqueId = `custom_pizza_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      let price = 0;
      if (pricingMethod === 'max') {
        price = Math.max(...quarters.map(q => q.price));
      } else {
        price = quarters.reduce((sum, q) => sum + q.price, 0) / quarters.length;
      }
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: 'custom_pizza',
        name: `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`,
        size: size,
        price: price,
        quantity: 1,
        addons: [],
        notes: notes,
        categoryId: categoryId,
        compositeType: 'pizza_quarters',
        quarters: quarters
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'INCREMENT_QUANTITY': {
      const updatedItems = state.items.map(item =>
        item.id === action.payload.itemId
          ? { ...item, quantity: item.quantity + 1 }
          : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'DECREMENT_QUANTITY': {
      const updatedItems = state.items.map(item =>
        item.id === action.payload.itemId && item.quantity > 1
          ? { ...item, quantity: item.quantity - 1 }
          : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'REMOVE_ITEM': {
      const updatedItems = state.items.filter(item => item.id !== action.payload.itemId);
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'SET_ORDER_TYPE':
      return { ...state, orderType: action.payload.orderType };
      
    case 'SET_TABLE':
      return { ...state, tableId: action.payload.tableId };

    case 'SET_NOTES':
      return { ...state, notes: action.payload.notes };
      
    default:
      return state;
  }
};

// Helper functions
const requiresTable = (orderType: OrderType): boolean => orderType === 'dine-in';
const requiresCustomerInfo = (orderType: OrderType): boolean => orderType === 'delivery' || orderType === 'takeaway';

// MenuItemCard component (matches NewOrderingInterface)
interface MenuItemCardProps {
  item: MenuItem;
  categoryId: string;
  isSelected: boolean;
  selectedSize: string | undefined;
  lastAddedItem: string | null;
  onAddItem: (item: MenuItem, size: string) => void;
}

const MenuItemCard: React.FC<MenuItemCardProps> = ({
  item,
  categoryId,
  isSelected,
  selectedSize,
  lastAddedItem,
  onAddItem
}) => {
  const handleSizeButtonClick = useCallback((e: React.MouseEvent, size: string) => {
    e.stopPropagation();
    onAddItem(item, size);
  }, [item, onAddItem]);

  return (
    <Card className={`transition-all duration-200 ${isSelected ? "ring-2 ring-primary/40 bg-primary/5" : "hover:shadow-sm"}`}>
      <CardContent className="p-3">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-semibold text-base truncate flex-1">{item.name}</h3>
        </div>

        {/* Compact size buttons */}
        <div className="flex gap-2 flex-wrap">
          {Object.entries(item.prices).map(([size, price]) => {
            const isItemSizeSelected = isSelected && selectedSize === size;
            const isJustAdded = lastAddedItem === `${item.id}-${size}`;

            return (
              <Button
                key={size}
                size="sm"
                variant={isItemSizeSelected ? "default" : isJustAdded ? "secondary" : "outline"}
                onClick={(e) => handleSizeButtonClick(e, size)}
                className="h-9 px-3 text-sm font-medium hover:scale-[1.02] transition-transform"
              >
                <span className="mr-2">
                  {size === 'default' ? 'Standard' : size}
                </span>
                <span className="font-bold tabular-nums">
                  {price as number} DA
                </span>
                {isJustAdded && <Check className="h-3 w-3 ml-1 text-green-600" />}
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

// MenuItemsGrid component
interface MenuItemsGridProps {
  categories: Category[] | undefined;
  selectedCategory: string;
  searchQuery: string;
  staffMenuItems: MenuItem[] | undefined;
  selectedItemForAddons: string | null;
  selectedItemSizes: {[key: string]: string};
  lastAddedItem: string | null;
  onAddItem: (item: MenuItem, size: string) => void;
}

const MenuItemsGrid: React.FC<MenuItemsGridProps> = ({
  categories,
  selectedCategory,
  searchQuery,
  staffMenuItems,
  selectedItemForAddons,
  selectedItemSizes,
  lastAddedItem,
  onAddItem
}) => {
  const currentCategory = categories?.find(cat => cat.id === selectedCategory);

  if (!currentCategory) return null;

  // Filter items based on search
  const filteredItems = currentCategory.items?.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  // Add staff menu items if they match the search
  const staffItems = staffMenuItems?.filter(item =>
    searchQuery && (
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  ) || [];

  const allItems = [...filteredItems, ...staffItems];

  if (allItems.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-muted-foreground">
          <Utensils className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <h3 className="font-medium text-lg mb-2">
            {searchQuery ? 'Aucun article trouvé' : 'Aucun article disponible'}
          </h3>
          <p className="text-sm">
            {searchQuery
              ? 'Essayez un autre terme de recherche'
              : 'Cette catégorie ne contient pas d\'articles pour le moment'
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-3">
      {allItems.map((item) => {
        const selectedSize = selectedItemSizes[item.id];
        const isSelected = selectedSize && selectedItemForAddons === `${item.id}-${selectedSize}`;

        return (
          <MenuItemCard
            key={item.id}
            item={item}
            categoryId={currentCategory.id}
            isSelected={isSelected}
            selectedSize={selectedSize}
            lastAddedItem={lastAddedItem}
            onAddItem={onAddItem}
          />
        );
      })}
    </div>
  );
};

export default function MobileWaiterInterface() {
  // Hooks
  const { user } = useAuth();
  const { categories, isLoading: menuLoading, isReady: menuReady } = useMenuV4();
  const { tables, isLoading: tablesLoading, isReady: tablesReady } = useTableDB();
  const { createOrder, isLoading: ordersLoading } = useOrderV4();
  const { staffMenuItems } = useStaffMenuV4();
  const { toast } = useToast();

  // State
  const [orderState, dispatch] = useReducer(orderReducer, initialOrderState);
  const [uiState, setUiState] = useState<UiState>(initialUiState);
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);

  // Helper functions for addon/note keys (matches NewOrderingInterface)
  const getAddonKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);
  const getItemNoteKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);

  // Set initial category when categories load
  useEffect(() => {
    if (categories?.length > 0 && !uiState.selectedCategory) {
      setUiState(prev => ({ ...prev, selectedCategory: categories[0].id }));
    }
  }, [categories, uiState.selectedCategory]);

  // Handle adding item (matches NewOrderingInterface flow)
  const handleAddItem = useCallback((item: MenuItem, size: string) => {
    const categoryId = uiState.selectedCategory;

    // Get current addons and notes for this item+size
    const addonKey = getAddonKey(item.id, size);
    const noteKey = getItemNoteKey(item.id, size);
    const selectedAddons = uiState.selectedAddons[addonKey] || new Set();
    const itemNotes = uiState.itemNotes[noteKey] || '';

    // Convert addons to OrderAddon format
    const category = categories?.find(cat => cat.id === categoryId);
    const addonObjects: OrderAddon[] = Array.from(selectedAddons).map(addonId => {
      const addon = category?.supplements?.find(s => s.id === addonId);
      return addon ? {
        id: addon.id,
        name: addon.name,
        price: addon.prices?.[size] || addon.prices?.['default'] || 0
      } : null;
    }).filter(Boolean) as OrderAddon[];

    // Add item to order
    dispatch({
      type: 'ADD_ITEM',
      payload: {
        item,
        size,
        addons: addonObjects,
        notes: itemNotes,
        categoryId: categoryId
      }
    });

    // Show success toast
    toast({
      title: "Article ajouté",
      description: `${item.name} (${size === 'default' ? 'Standard' : size}) ajouté au panier`,
      duration: 2000,
    });

    // Set last added item for visual feedback
    setUiState(prev => ({ ...prev, lastAddedItem: `${item.id}-${size}` }));

    // Clear the visual feedback after 2 seconds
    setTimeout(() => {
      setUiState(prev => ({ ...prev, lastAddedItem: null }));
    }, 2000);

    // Open customization panel after adding item
    setUiState(prev => ({
      ...prev,
      selectedItemForAddons: `${item.id}-${size}`,
      selectedItemSizes: { ...prev.selectedItemSizes, [item.id]: size }
    }));
  }, [uiState.selectedAddons, uiState.itemNotes, uiState.selectedCategory, getAddonKey, getItemNoteKey, categories, dispatch, toast]);

  // Place order function (simplified for dine-in only)
  const handlePlaceOrder = useCallback(async () => {
    if (!orderState.items?.length) return;
    if (!orderState.tableId) return;

    setIsPlacingOrder(true);

    try {
      const newOrder: NewOrder = {
        tableId: orderState.tableId,
        orderType: 'dine-in',
        status: 'pending',
        items: orderState.items.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
        total: orderState.total,
        notes: orderState.notes,
        paymentStatus: 'unpaid',
        createdBy: user?.name || (user as any)?.username || 'unknown',
        createdByName: user?.name || 'Personnel Inconnu'
      };

      await createOrder(newOrder);

      toast({
        title: "Commande créée",
        description: "La commande a été créée avec succès.",
      });

      // Reset order
      dispatch({ type: 'INITIALIZE_ORDER' });
      setUiState(initialUiState);

    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: "Erreur",
        description: "Impossible de créer la commande. Veuillez réessayer.",
        variant: "destructive"
      });
    } finally {
      setIsPlacingOrder(false);
    }
  }, [orderState, user, createOrder, toast]);

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Mobile Header */}
      <div className="flex-shrink-0 p-3 border-b bg-background/95 backdrop-blur">
        <div className="flex items-center justify-between mb-3">
          <h1 className="text-lg font-semibold">Serveur</h1>
          <div className="flex items-center gap-2 text-sm">
            <Utensils className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">Sur Place</span>
            {orderState.tableId && (
              <>
                <span className="text-muted-foreground">•</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => dispatch({ type: 'SET_TABLE', payload: { tableId: '' } })}
                  className="h-auto p-1 font-medium text-primary hover:text-primary/80"
                >
                  Table {tables?.find(t => t.id === orderState.tableId)?.name}
                </Button>
              </>
            )}
          </div>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher..."
            value={uiState.searchQuery}
            onChange={(e) => setUiState(prev => ({ ...prev, searchQuery: e.target.value }))}
            className="pl-10 h-9 text-base"
          />
        </div>
      </div>

      {/* Category Navigation */}
      <div className="flex-shrink-0 border-b bg-background">
        <ScrollArea className="w-full">
          <div className="flex gap-3 p-4">
            {menuLoading ? (
              // Loading skeleton for categories
              [...Array(4)].map((_, i) => (
                <div key={i} className="h-10 w-24 bg-muted rounded-lg animate-pulse flex-shrink-0" />
              ))
            ) : (
              categories?.map((category) => (
                <Button
                  key={category.id}
                  variant={uiState.selectedCategory === category.id ? "default" : "outline"}
                  size="lg"
                  onClick={() => setUiState(prev => ({ ...prev, selectedCategory: category.id }))}
                  className="whitespace-nowrap h-10 px-4 text-sm font-medium flex-shrink-0 transition-all"
                  style={{
                    backgroundColor: uiState.selectedCategory === category.id ? category.color : undefined,
                    borderColor: category.color,
                    color: uiState.selectedCategory === category.id ? 'white' : category.color
                  }}
                >
                  {category.name}
                  {category.items && (
                    <Badge
                      variant="secondary"
                      className="ml-2 text-xs"
                      style={{
                        backgroundColor: uiState.selectedCategory === category.id ? 'rgba(255,255,255,0.2)' : undefined
                      }}
                    >
                      {category.items.length}
                    </Badge>
                  )}
                </Button>
              ))
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Menu Items */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-3">
            {menuLoading ? (
              <div className="grid grid-cols-1 gap-3">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-20 bg-muted rounded-lg animate-pulse" />
                ))}
              </div>
            ) : (
              <MenuItemsGrid
                categories={categories}
                selectedCategory={uiState.selectedCategory}
                searchQuery={uiState.searchQuery}
                staffMenuItems={staffMenuItems}
                selectedItemForAddons={uiState.selectedItemForAddons}
                selectedItemSizes={uiState.selectedItemSizes}
                lastAddedItem={uiState.lastAddedItem}
                onAddItem={handleAddItem}
              />
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Mobile Bottom Navigation - Enhanced */}
      <div className="flex-shrink-0 border-t bg-background/95 backdrop-blur">
        {orderState.items.length > 0 ? (
          <div className="p-3 space-y-3">
            {/* Order Summary */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <span className="font-medium">
                  {orderState.items.reduce((sum, item) => sum + item.quantity, 0)} articles
                </span>
                {orderState.tableId && (
                  <>
                    <span>•</span>
                    <span className="text-muted-foreground">
                      Table {tables?.find(t => t.id === orderState.tableId)?.name}
                    </span>
                  </>
                )}
              </div>
              <div className="font-bold text-lg text-primary">
                {orderState.total.toFixed(2)} DA
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setUiState(prev => ({ ...prev, showCart: true }))}
                className="flex-1 h-12"
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                Voir le panier
              </Button>

              {orderState.tableId ? (
                <Button
                  onClick={handlePlaceOrder}
                  disabled={isPlacingOrder}
                  className="flex-1 h-12 font-semibold"
                >
                  {isPlacingOrder ? 'Envoi...' : 'Commander'}
                </Button>
              ) : (
                <Button
                  variant="secondary"
                  disabled
                  className="flex-1 h-12"
                >
                  Sélectionner une table
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="p-4 text-center">
            <div className="text-sm text-muted-foreground">
              Ajoutez des articles au panier pour commencer
            </div>
          </div>
        )}
      </div>

      {/* Cart Sheet - Enhanced Mobile UX */}
      <Sheet open={uiState.showCart} onOpenChange={(open) => setUiState(prev => ({ ...prev, showCart: open }))}>
        <SheetContent side="right" className="w-full sm:max-w-md">
          <SheetHeader className="pb-4">
            <SheetTitle className="text-xl">
              Panier ({orderState.items.reduce((sum, item) => sum + item.quantity, 0)})
            </SheetTitle>
          </SheetHeader>

          <div className="flex flex-col h-full">
            <ScrollArea className="flex-1">
              {orderState.items.length === 0 ? (
                <div className="text-center py-12">
                  <ShoppingCart className="h-16 w-16 mx-auto text-muted-foreground mb-4 opacity-50" />
                  <h3 className="font-medium text-lg mb-2">Votre panier est vide</h3>
                  <p className="text-muted-foreground text-sm">
                    Ajoutez des articles depuis le menu
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {orderState.items.map((item) => (
                    <Card
                      key={item.id}
                      className="border-l-4 border-l-primary/20 cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => {
                        // Open customization for this item
                        setUiState(prev => ({
                          ...prev,
                          selectedItemForAddons: `${item.menuItemId}-${item.size}`,
                          selectedItemSizes: { ...prev.selectedItemSizes, [item.menuItemId]: item.size },
                          showCart: false
                        }));
                      }}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <h4 className="font-semibold text-base truncate">{item.name}</h4>
                              <span className="text-xs text-muted-foreground">Tap to edit</span>
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              {item.size !== 'default' && <span className="font-medium">Taille: {item.size} • </span>}
                              <span className="font-medium">{item.price} DA</span>
                            </div>
                            {item.addons && item.addons.length > 0 && (
                              <div className="text-sm text-muted-foreground mt-2">
                                <span className="font-medium">Suppléments:</span>
                                <div className="mt-1">
                                  {item.addons.map(addon => (
                                    <div key={addon.id} className="flex justify-between">
                                      <span>+ {addon.name}</span>
                                      <span>+{addon.price} DA</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                            {item.notes && (
                              <div className="text-sm text-muted-foreground mt-2 p-2 bg-muted/50 rounded">
                                <span className="font-medium">Note:</span> {item.notes}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Quantity controls - Mobile optimized */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                dispatch({ type: 'DECREMENT_QUANTITY', payload: { itemId: item.id } });
                              }}
                              className="h-8 w-8 p-0"
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                            <span className="text-lg font-semibold w-8 text-center">{item.quantity}</span>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                dispatch({ type: 'INCREMENT_QUANTITY', payload: { itemId: item.id } });
                              }}
                              className="h-8 w-8 p-0"
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>

                          <div className="flex items-center gap-3">
                            <span className="font-bold text-lg">
                              {((item.price + (item.addons?.reduce((sum, addon) => sum + addon.price, 0) || 0)) * item.quantity).toFixed(2)} DA
                            </span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                dispatch({ type: 'REMOVE_ITEM', payload: { itemId: item.id } });
                              }}
                              className="h-8 w-8 p-0 text-destructive hover:bg-destructive/10"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </ScrollArea>

            {orderState.items.length > 0 && (
              <div className="border-t pt-4 mt-4 space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold">Total:</span>
                  <span className="text-2xl font-bold text-primary">{orderState.total.toFixed(2)} DA</span>
                </div>

                {orderState.tableId && (
                  <Button
                    onClick={handlePlaceOrder}
                    disabled={isPlacingOrder}
                    className="w-full h-12 text-lg font-semibold"
                  >
                    {isPlacingOrder ? 'Envoi en cours...' : 'Passer la commande'}
                  </Button>
                )}

                {!orderState.tableId && (
                  <div className="text-center text-sm text-muted-foreground">
                    Sélectionnez une table pour continuer
                  </div>
                )}
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Item Customization Panel (inline like NewOrderingInterface) */}
      {uiState.selectedItemForAddons && (
        <ItemCustomizationPanel
          categories={categories}
          uiState={uiState}
          setUiState={setUiState}
          dispatch={dispatch}
          orderState={orderState}
          getAddonKey={getAddonKey}
          getItemNoteKey={getItemNoteKey}
          onClose={() => {
            setUiState(prev => ({ ...prev, selectedItemForAddons: null }));
          }}
        />
      )}

      {/* Floating Cart Button - Shows when items in cart */}
      {orderState.items.length > 0 && !uiState.showCart && !uiState.selectedItemForAddons && (
        <div className="fixed bottom-20 right-4 z-40">
          <Button
            onClick={() => setUiState(prev => ({ ...prev, showCart: true }))}
            className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 relative"
          >
            <ShoppingCart className="h-6 w-6" />
            <Badge className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 flex items-center justify-center text-xs font-bold">
              {orderState.items.reduce((sum, item) => sum + item.quantity, 0)}
            </Badge>
          </Button>
        </div>
      )}

      {/* Table Selection Dialog */}
      <TableSelectionDialog
        isOpen={!orderState.tableId}
        tables={tables}
        isLoading={tablesLoading}
        onTableSelect={(tableId) => dispatch({ type: 'SET_TABLE', payload: { tableId } })}
      />
    </div>
  );
}

// ItemCustomizationPanel Component (matches NewOrderingInterface)
interface ItemCustomizationPanelProps {
  categories: Category[] | undefined;
  uiState: UiState;
  setUiState: React.Dispatch<React.SetStateAction<UiState>>;
  dispatch: React.Dispatch<OrderAction>;
  orderState: OrderState;
  getAddonKey: (itemId: string, size: string) => string;
  getItemNoteKey: (itemId: string, size: string) => string;
  onClose: () => void;
}

const ItemCustomizationPanel: React.FC<ItemCustomizationPanelProps> = ({
  categories,
  uiState,
  setUiState,
  dispatch,
  orderState,
  getAddonKey,
  getItemNoteKey,
  onClose
}) => {
  if (!uiState.selectedItemForAddons) return null;

  const [itemId, selectedSize] = uiState.selectedItemForAddons.split('-');

  // Find the item and category
  let item: MenuItem | undefined;
  let category: Category | undefined;

  for (const cat of categories || []) {
    const foundItem = cat.items?.find(i => i.id === itemId);
    if (foundItem) {
      item = foundItem;
      category = cat;
      break;
    }
  }

  if (!item || !category) return null;

  const addonKey = getAddonKey(itemId, selectedSize);
  const noteKey = getItemNoteKey(itemId, selectedSize);
  const currentAddons = uiState.selectedAddons[addonKey] || new Set();
  const currentNote = uiState.itemNotes[noteKey] || '';

  const toggleAddon = (addonId: string) => {
    const newAddons = new Set(currentAddons);
    if (newAddons.has(addonId)) {
      newAddons.delete(addonId);
    } else {
      newAddons.add(addonId);
    }
    setUiState(prev => ({
      ...prev,
      selectedAddons: { ...prev.selectedAddons, [addonKey]: newAddons }
    }));

    // Update the actual order item immediately
    updateOrderItem(newAddons, currentNote);
  };

  const updateNote = (note: string) => {
    setUiState(prev => ({
      ...prev,
      itemNotes: { ...prev.itemNotes, [noteKey]: note }
    }));

    // Update the actual order item immediately
    updateOrderItem(currentAddons, note);
  };

  const updateOrderItem = (addons: Set<string>, notes: string) => {
    // Find the most recent item that matches this item+size
    const orderItem = [...orderState.items].reverse().find(oi =>
      oi.menuItemId === itemId && oi.size === selectedSize
    );

    if (orderItem) {
      // Convert addons to OrderAddon format
      const addonObjects: OrderAddon[] = Array.from(addons).map(addonId => {
        const addon = category?.supplements?.find(s => s.id === addonId);
        return addon ? {
          id: addon.id,
          name: addon.name,
          price: addon.prices?.[selectedSize] || addon.prices?.['default'] || 0
        } : null;
      }).filter(Boolean) as OrderAddon[];

      dispatch({
        type: 'UPDATE_ITEM',
        payload: {
          itemId: orderItem.id,
          addons: addonObjects,
          notes: notes
        }
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b bg-background">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-semibold">{item.name}</h2>
            <Badge variant="secondary" className="text-sm">
              {selectedSize === 'default' ? 'Standard' : selectedSize}
            </Badge>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-10 w-10">
            <X className="h-5 w-5" />
          </Button>
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          Personnalisez votre commande
        </p>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {/* Add-ons */}
          {category.supplements && category.supplements.length > 0 && (
            <div>
              <h3 className="font-semibold text-lg mb-4">Suppléments disponibles</h3>
              <div className="space-y-3">
                {category.supplements.map((addon) => {
                  const addonPrice = addon.prices?.[selectedSize] || addon.prices?.['default'] || 0;
                  const isSelected = currentAddons.has(addon.id);

                  return (
                    <div
                      key={addon.id}
                      className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                        isSelected
                          ? 'border-primary bg-primary/10 shadow-md'
                          : 'border-border hover:border-primary/50 hover:shadow-sm'
                      }`}
                      onClick={() => toggleAddon(addon.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="font-semibold text-base">{addon.name}</div>
                          <div className="text-sm text-muted-foreground mt-1">
                            Supplément: +{addonPrice} DA
                          </div>
                        </div>
                        <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors ${
                          isSelected
                            ? 'border-primary bg-primary'
                            : 'border-muted-foreground/30'
                        }`}>
                          {isSelected && <Check className="h-4 w-4 text-white" />}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {currentAddons.size > 0 && (
                <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                  <div className="text-sm font-medium">
                    Suppléments sélectionnés: +{Array.from(currentAddons).reduce((total, addonId) => {
                      const addon = category.supplements?.find(s => s.id === addonId);
                      return total + (addon?.prices?.[selectedSize] || addon?.prices?.['default'] || 0);
                    }, 0)} DA
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Notes */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Instructions spéciales</h3>
            <Textarea
              placeholder="Ajoutez des instructions particulières pour cet article..."
              value={currentNote}
              onChange={(e) => updateNote(e.target.value)}
              className="min-h-[100px] text-base resize-none"
            />
            <div className="text-xs text-muted-foreground mt-2">
              Exemple: Sans oignons, bien cuit, sauce à part...
            </div>
          </div>
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="flex-shrink-0 p-4 border-t bg-background">
        <Button
          onClick={() => setUiState(prev => ({ ...prev, selectedItemForAddons: null }))}
          className="w-full h-12 text-lg font-semibold"
        >
          Confirmer les modifications
        </Button>
      </div>
    </div>
  );
};

// TableSelectionDialog Component (Enhanced Mobile UX)
interface TableSelectionDialogProps {
  isOpen: boolean;
  tables: Table[] | undefined;
  isLoading: boolean;
  onTableSelect: (tableId: string) => void;
}

const TableSelectionDialog: React.FC<TableSelectionDialogProps> = ({
  isOpen,
  tables,
  isLoading,
  onTableSelect
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b bg-background">
        <div className="text-center">
          <h1 className="text-xl font-semibold">Sélectionner une table</h1>
          <p className="text-sm text-muted-foreground mt-1">
            Choisissez une table pour commencer la commande
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-4">
            {isLoading ? (
              <div className="grid grid-cols-2 gap-3">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="h-20 bg-muted rounded-lg animate-pulse" />
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-3">
                {tables?.map((table) => (
                  <Button
                    key={table.id}
                    variant="outline"
                    onClick={() => onTableSelect(table.id)}
                    className="h-20 flex flex-col gap-1 text-center hover:bg-primary hover:text-primary-foreground transition-colors"
                  >
                    <div className="text-sm font-medium text-muted-foreground">Table</div>
                    <div className="text-2xl font-bold">{table.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {table.seats} places
                    </div>
                  </Button>
                ))}
              </div>
            )}

            {!isLoading && (!tables || tables.length === 0) && (
              <div className="text-center py-12">
                <div className="text-muted-foreground">
                  <Utensils className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Aucune table disponible</p>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};


