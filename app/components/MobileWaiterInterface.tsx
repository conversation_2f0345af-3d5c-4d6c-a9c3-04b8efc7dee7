"use client";

import React, { useState, useEffect, useCallback, useReducer, useMemo } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { useOrderV4 } from '@/lib/hooks/use-order-v4';
import { useTableDB } from '@/lib/hooks/useTableDB';
import { useStaffMenuV4 } from '@/lib/hooks/use-staff-menu-v4';
import { useToast } from '@/components/ui/use-toast';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent } from '@/components/ui/card';

// Icons
import { 
  Search, 
  ShoppingCart, 
  Plus, 
  Minus, 
  X,
  Pizza,
  Check,
  ChevronRight,
  MapPin,
  Phone,
  User,
  Utensils,
  Package,
  Truck
} from 'lucide-react';

// Types
import type { MenuItem, Addon, Category } from '@/lib/db/v4-menu-service';
import type { OrderItem, OrderAddon, NewOrder } from '@/lib/types';
import type { OrderType } from '@/lib/types/order-types';
import type { PizzaQuarter } from '@/lib/types/pizza-types';
import { TableLayout as Table } from '@/lib/db/table-db';

// Order state interface
interface OrderState {
  _id: string;
  id: string;
  type: string;
  orderType: OrderType;
  status: string;
  tableId: string;
  items: OrderItem[];
  total: number;
  createdAt: string;
  updatedAt: string;
  schemaVersion: string;
  notes: string;
  customer?: {
    name?: string;
    phone?: string;
    address?: string;
  };
  deliveryPerson?: string;
}

// UI state interface
interface UiState {
  selectedCategory: string;
  selectedItemForCustomization: string | null;
  selectedItemSizes: {[key: string]: string};
  itemNotes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  searchQuery: string;
  showCart: boolean;
  showOrderTypeSelector: boolean;
  showCustomerInfo: boolean;
  showPizzaBuilder: boolean;
  pizzaQuarters: (PizzaQuarter | null)[];
  pizzaPricingMethod: 'max' | 'average';
}

// Order actions
type OrderAction = 
  | { type: 'INITIALIZE_ORDER' }
  | { type: 'ADD_ITEM', payload: { item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string } }
  | { type: 'ADD_CUSTOM_PIZZA', payload: { quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average' } }
  | { type: 'INCREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'DECREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'REMOVE_ITEM', payload: { itemId: string } }
  | { type: 'SET_ORDER_TYPE', payload: { orderType: OrderType } }
  | { type: 'SET_TABLE', payload: { tableId: string } }
  | { type: 'SET_CUSTOMER', payload: { customer: any } }
  | { type: 'SET_NOTES', payload: { notes: string } };

// Initial states
const initialOrderState: OrderState = {
  _id: "",
  id: "",
  type: "order_document",
  orderType: "dine-in",
  status: "pending",
  tableId: "",
  items: [],
  total: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  schemaVersion: "v4.0",
  notes: ""
};

const initialUiState: UiState = {
  selectedCategory: "",
  selectedItemForCustomization: null,
  selectedItemSizes: {},
  itemNotes: {},
  selectedAddons: {},
  searchQuery: "",
  showCart: false,
  showOrderTypeSelector: false,
  showCustomerInfo: false,
  showPizzaBuilder: false,
  pizzaQuarters: [null, null, null, null],
  pizzaPricingMethod: 'max'
};

// Calculate total helper
const calculateTotal = (items: OrderItem[]): number => {
  return items.reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const addonsTotal = item.addons?.reduce((sum, addon) => sum + (addon.price * item.quantity), 0) || 0;
    return total + itemTotal + addonsTotal;
  }, 0);
};

// Order reducer
const orderReducer = (state: OrderState, action: OrderAction): OrderState => {
  switch (action.type) {
    case 'INITIALIZE_ORDER':
      return { ...initialOrderState };
      
    case 'ADD_ITEM': {
      const { item, size, addons, notes, categoryId } = action.payload;
      const price = item.prices[size] || Object.values(item.prices)[0] || 0;
      const uniqueId = `order_item_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: item.id,
        name: item.name,
        size: size,
        price: price,
        quantity: 1,
        addons: addons,
        notes: notes,
        categoryId: categoryId
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'ADD_CUSTOM_PIZZA': {
      const { quarters, size, notes, categoryId, pricingMethod } = action.payload;
      const uniqueId = `custom_pizza_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      let price = 0;
      if (pricingMethod === 'max') {
        price = Math.max(...quarters.map(q => q.price));
      } else {
        price = quarters.reduce((sum, q) => sum + q.price, 0) / quarters.length;
      }
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: 'custom_pizza',
        name: `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`,
        size: size,
        price: price,
        quantity: 1,
        addons: [],
        notes: notes,
        categoryId: categoryId,
        compositeType: 'pizza_quarters',
        quarters: quarters
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'INCREMENT_QUANTITY': {
      const updatedItems = state.items.map(item =>
        item.id === action.payload.itemId
          ? { ...item, quantity: item.quantity + 1 }
          : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'DECREMENT_QUANTITY': {
      const updatedItems = state.items.map(item =>
        item.id === action.payload.itemId && item.quantity > 1
          ? { ...item, quantity: item.quantity - 1 }
          : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'REMOVE_ITEM': {
      const updatedItems = state.items.filter(item => item.id !== action.payload.itemId);
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'SET_ORDER_TYPE':
      return { ...state, orderType: action.payload.orderType };
      
    case 'SET_TABLE':
      return { ...state, tableId: action.payload.tableId };
      
    case 'SET_CUSTOMER':
      return { ...state, customer: action.payload.customer };
      
    case 'SET_NOTES':
      return { ...state, notes: action.payload.notes };
      
    default:
      return state;
  }
};

// Helper functions
const requiresTable = (orderType: OrderType): boolean => orderType === 'dine-in';
const requiresCustomerInfo = (orderType: OrderType): boolean => orderType === 'delivery' || orderType === 'takeaway';

// MenuItemsGrid component
interface MenuItemsGridProps {
  categories: Category[] | undefined;
  selectedCategory: string;
  searchQuery: string;
  staffMenuItems: MenuItem[] | undefined;
  onItemSelect: (item: MenuItem, category: Category) => void;
  onPizzaCustomize: (availablePizzas: MenuItem[], category: Category) => void;
}

const MenuItemsGrid: React.FC<MenuItemsGridProps> = ({
  categories,
  selectedCategory,
  searchQuery,
  staffMenuItems,
  onItemSelect,
  onPizzaCustomize
}) => {
  const currentCategory = categories?.find(cat => cat.id === selectedCategory);

  if (!currentCategory) return null;

  // Filter items based on search
  const filteredItems = currentCategory.items?.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  // Add staff menu items if they match the search
  const staffItems = staffMenuItems?.filter(item =>
    searchQuery && (
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  ) || [];

  const allItems = [...filteredItems, ...staffItems];

  if (allItems.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          {searchQuery ? 'Aucun article trouvé' : 'Aucun article dans cette catégorie'}
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-3">
      {/* Pizza customization button for pizza categories */}
      {currentCategory.name.toLowerCase().includes('pizza') && (
        <Card className="border-dashed border-2 border-primary/30">
          <CardContent className="p-4">
            <Button
              variant="ghost"
              onClick={() => onPizzaCustomize(filteredItems, currentCategory)}
              className="w-full h-auto flex flex-col items-center gap-2 py-4"
            >
              <Pizza className="h-8 w-8 text-primary" />
              <div className="text-center">
                <div className="font-medium">Pizza Personnalisée</div>
                <div className="text-xs text-muted-foreground">Créer votre propre pizza</div>
              </div>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Menu items */}
      {allItems.map((item) => (
        <Card key={item.id} className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-medium text-sm truncate">{item.name}</h3>
                  {staffItems.includes(item) && (
                    <Badge variant="secondary" className="text-xs">Staff</Badge>
                  )}
                </div>
                {item.description && (
                  <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                    {item.description}
                  </p>
                )}
                <div className="flex items-center gap-2">
                  {Object.entries(item.prices).map(([size, price]) => (
                    <Badge key={size} variant="outline" className="text-xs">
                      {size === 'default' ? 'Prix' : size}: {price} DA
                    </Badge>
                  ))}
                </div>
              </div>
              <Button
                size="sm"
                onClick={() => onItemSelect(item, currentCategory)}
                className="ml-3 h-8 w-8 p-0"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default function MobileWaiterInterface() {
  // Hooks
  const { user } = useAuth();
  const { categories, isLoading: menuLoading, isReady: menuReady } = useMenuV4();
  const { tables, isLoading: tablesLoading, isReady: tablesReady } = useTableDB();
  const { createOrder, isLoading: ordersLoading } = useOrderV4();
  const { staffMenuItems } = useStaffMenuV4();
  const { toast } = useToast();

  // State
  const [orderState, dispatch] = useReducer(orderReducer, initialOrderState);
  const [uiState, setUiState] = useState<UiState>(initialUiState);
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);

  // Set initial category when categories load
  useEffect(() => {
    if (categories?.length > 0 && !uiState.selectedCategory) {
      setUiState(prev => ({ ...prev, selectedCategory: categories[0].id }));
    }
  }, [categories, uiState.selectedCategory]);

  // Place order function
  const handlePlaceOrder = useCallback(async () => {
    if (!orderState.items?.length) return;
    if (requiresTable(orderState.orderType) && !orderState.tableId) return;

    setIsPlacingOrder(true);

    try {
      const newOrder: NewOrder = {
        tableId: orderState.orderType === 'dine-in' ? orderState.tableId : '',
        orderType: orderState.orderType,
        status: 'pending',
        items: orderState.items.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
        total: orderState.total,
        notes: orderState.notes,
        customer: requiresCustomerInfo(orderState.orderType) ? orderState.customer : undefined,
        paymentStatus: 'unpaid',
        createdBy: user?.name || (user as any)?.username || 'unknown',
        createdByName: user?.name || 'Personnel Inconnu'
      };

      await createOrder(newOrder);

      toast({
        title: "Commande créée",
        description: "La commande a été créée avec succès.",
      });

      // Reset order
      dispatch({ type: 'INITIALIZE_ORDER' });
      setUiState(initialUiState);

    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: "Erreur",
        description: "Impossible de créer la commande. Veuillez réessayer.",
        variant: "destructive"
      });
    } finally {
      setIsPlacingOrder(false);
    }
  }, [orderState, user, createOrder, toast]);

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Mobile Header */}
      <div className="flex-shrink-0 p-3 border-b bg-background/95 backdrop-blur">
        <div className="flex items-center justify-between mb-3">
          <h1 className="text-lg font-semibold">Serveur</h1>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setUiState(prev => ({ ...prev, showOrderTypeSelector: true }))}
            className="h-8 text-xs"
          >
            {orderState.orderType === 'dine-in' && <Utensils className="h-3 w-3 mr-1" />}
            {orderState.orderType === 'takeaway' && <Package className="h-3 w-3 mr-1" />}
            {orderState.orderType === 'delivery' && <Truck className="h-3 w-3 mr-1" />}
            {orderState.orderType === 'dine-in' ? 'Sur Place' : 
             orderState.orderType === 'takeaway' ? 'Emporter' : 'Livraison'}
          </Button>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher..."
            value={uiState.searchQuery}
            onChange={(e) => setUiState(prev => ({ ...prev, searchQuery: e.target.value }))}
            className="pl-10 h-9 text-base"
          />
        </div>
      </div>

      {/* Category Navigation */}
      <div className="flex-shrink-0 border-b bg-background">
        <ScrollArea className="w-full">
          <div className="flex gap-2 p-3">
            {categories?.map((category) => (
              <Button
                key={category.id}
                variant={uiState.selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setUiState(prev => ({ ...prev, selectedCategory: category.id }))}
                className="whitespace-nowrap h-8 text-xs"
                style={{
                  backgroundColor: uiState.selectedCategory === category.id ? category.color : undefined,
                  borderColor: category.color,
                  color: uiState.selectedCategory === category.id ? 'white' : category.color
                }}
              >
                {category.name}
              </Button>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Menu Items */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-3">
            {menuLoading ? (
              <div className="grid grid-cols-1 gap-3">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-20 bg-muted rounded-lg animate-pulse" />
                ))}
              </div>
            ) : (
              <MenuItemsGrid
                categories={categories}
                selectedCategory={uiState.selectedCategory}
                searchQuery={uiState.searchQuery}
                staffMenuItems={staffMenuItems}
                onItemSelect={(item, category) => {
                  // Set default size and open customization
                  const defaultSize = Object.keys(item.prices)[0] || 'default';
                  setUiState(prev => ({
                    ...prev,
                    selectedItemForCustomization: `${item.id}-${defaultSize}`,
                    selectedItemSizes: { ...prev.selectedItemSizes, [item.id]: defaultSize }
                  }));
                }}
                onPizzaCustomize={(availablePizzas, category) => {
                  setUiState(prev => ({
                    ...prev,
                    showPizzaBuilder: true,
                    selectedCategory: category.id
                  }));
                }}
              />
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="flex-shrink-0 border-t bg-background/95 backdrop-blur p-3">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setUiState(prev => ({ ...prev, showCart: true }))}
            className="flex-1 h-12 relative"
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            Panier ({orderState.items.length})
            {orderState.items.length > 0 && (
              <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                {orderState.items.reduce((sum, item) => sum + item.quantity, 0)}
              </Badge>
            )}
          </Button>
          
          {orderState.items.length > 0 && (
            <div className="text-right">
              <div className="text-sm font-medium">{orderState.total.toFixed(2)} DA</div>
              <div className="text-xs text-muted-foreground">Total</div>
            </div>
          )}
        </div>
      </div>

      {/* Cart Sheet */}
      <Sheet open={uiState.showCart} onOpenChange={(open) => setUiState(prev => ({ ...prev, showCart: open }))}>
        <SheetContent side="right" className="w-full sm:max-w-md">
          <SheetHeader>
            <SheetTitle>Panier ({orderState.items.length})</SheetTitle>
          </SheetHeader>
          <div className="flex flex-col h-full">
            <ScrollArea className="flex-1 mt-4">
              {orderState.items.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">Votre panier est vide</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {orderState.items.map((item) => (
                    <Card key={item.id}>
                      <CardContent className="p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm truncate">{item.name}</h4>
                            <div className="text-xs text-muted-foreground mt-1">
                              {item.size !== 'default' && <span>Taille: {item.size} • </span>}
                              {item.price} DA
                            </div>
                            {item.addons && item.addons.length > 0 && (
                              <div className="text-xs text-muted-foreground mt-1">
                                Suppléments: {item.addons.map(addon => addon.name).join(', ')}
                              </div>
                            )}
                            {item.notes && (
                              <div className="text-xs text-muted-foreground mt-1">
                                Note: {item.notes}
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2 ml-3">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => dispatch({ type: 'DECREMENT_QUANTITY', payload: { itemId: item.id } })}
                              className="h-6 w-6 p-0"
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="text-sm font-medium w-6 text-center">{item.quantity}</span>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => dispatch({ type: 'INCREMENT_QUANTITY', payload: { itemId: item.id } })}
                              className="h-6 w-6 p-0"
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => dispatch({ type: 'REMOVE_ITEM', payload: { itemId: item.id } })}
                              className="h-6 w-6 p-0 text-destructive"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </ScrollArea>

            {orderState.items.length > 0 && (
              <div className="border-t pt-4 mt-4">
                <div className="flex justify-between items-center mb-4">
                  <span className="font-semibold">Total:</span>
                  <span className="font-semibold text-lg">{orderState.total.toFixed(2)} DA</span>
                </div>
                <Button
                  onClick={() => {
                    setUiState(prev => ({ ...prev, showCart: false, showOrderTypeSelector: true }));
                  }}
                  className="w-full"
                  disabled={orderState.items.length === 0}
                >
                  Continuer la commande
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Item Customization Modal */}
      <ItemCustomizationModal
        isOpen={!!uiState.selectedItemForCustomization}
        onClose={() => setUiState(prev => ({ ...prev, selectedItemForCustomization: null }))}
        categories={categories}
        uiState={uiState}
        setUiState={setUiState}
        dispatch={dispatch}
        staffMenuItems={staffMenuItems}
      />

      {/* Order Type Selector */}
      <OrderTypeSelector
        isOpen={uiState.showOrderTypeSelector}
        onClose={() => setUiState(prev => ({ ...prev, showOrderTypeSelector: false }))}
        orderState={orderState}
        dispatch={dispatch}
        tables={tables}
        onContinue={() => {
          setUiState(prev => ({ ...prev, showOrderTypeSelector: false }));
          if (requiresCustomerInfo(orderState.orderType)) {
            setUiState(prev => ({ ...prev, showCustomerInfo: true }));
          } else {
            handlePlaceOrder();
          }
        }}
      />

      {/* Customer Info Modal */}
      <CustomerInfoModal
        isOpen={uiState.showCustomerInfo}
        onClose={() => setUiState(prev => ({ ...prev, showCustomerInfo: false }))}
        orderState={orderState}
        dispatch={dispatch}
        onContinue={handlePlaceOrder}
      />

      {/* Pizza Builder Modal */}
      <PizzaBuilderModal
        isOpen={uiState.showPizzaBuilder}
        onClose={() => setUiState(prev => ({ ...prev, showPizzaBuilder: false }))}
        categories={categories}
        selectedCategory={uiState.selectedCategory}
        uiState={uiState}
        setUiState={setUiState}
        dispatch={dispatch}
      />
    </div>
  );
}

// Item Customization Modal Component
interface ItemCustomizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  categories: Category[] | undefined;
  uiState: UiState;
  setUiState: React.Dispatch<React.SetStateAction<UiState>>;
  dispatch: React.Dispatch<OrderAction>;
  staffMenuItems: MenuItem[] | undefined;
}

const ItemCustomizationModal: React.FC<ItemCustomizationModalProps> = ({
  isOpen,
  onClose,
  categories,
  uiState,
  setUiState,
  dispatch,
  staffMenuItems
}) => {
  if (!isOpen || !uiState.selectedItemForCustomization) return null;

  const [itemId, selectedSize] = uiState.selectedItemForCustomization.split('-');

  // Find the item
  let item: MenuItem | undefined;
  let category: Category | undefined;

  // Search in regular categories
  for (const cat of categories || []) {
    const foundItem = cat.items?.find(i => i.id === itemId);
    if (foundItem) {
      item = foundItem;
      category = cat;
      break;
    }
  }

  // Search in staff menu if not found
  if (!item) {
    item = staffMenuItems?.find(i => i.id === itemId);
    category = categories?.[0]; // Use first category as fallback
  }

  if (!item || !category) return null;

  const addonKey = `${itemId}-${selectedSize}`;
  const noteKey = `${itemId}-${selectedSize}`;
  const currentAddons = uiState.selectedAddons[addonKey] || new Set();
  const currentNote = uiState.itemNotes[noteKey] || '';

  const handleAddToCart = () => {
    const addons: OrderAddon[] = Array.from(currentAddons).map(addonId => {
      const addon = category?.supplements?.find(s => s.id === addonId);
      return addon ? {
        id: addon.id,
        name: addon.name,
        price: addon.prices?.[selectedSize] || addon.prices?.['default'] || 0
      } : null;
    }).filter(Boolean) as OrderAddon[];

    dispatch({
      type: 'ADD_ITEM',
      payload: {
        item,
        size: selectedSize,
        addons,
        notes: currentNote,
        categoryId: category.id
      }
    });

    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{item.name}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Size Selection */}
          <div>
            <h4 className="font-medium mb-2">Taille</h4>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(item.prices).map(([size, price]) => (
                <Button
                  key={size}
                  variant={selectedSize === size ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    const newKey = `${itemId}-${size}`;
                    setUiState(prev => ({
                      ...prev,
                      selectedItemForCustomization: newKey,
                      selectedItemSizes: { ...prev.selectedItemSizes, [itemId]: size }
                    }));
                  }}
                  className="h-auto py-2 flex flex-col"
                >
                  <span className="font-medium">{size === 'default' ? 'Standard' : size}</span>
                  <span className="text-xs">{price} DA</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Add-ons */}
          {category.supplements && category.supplements.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Suppléments</h4>
              <div className="space-y-2">
                {category.supplements.map((addon) => {
                  const addonPrice = addon.prices?.[selectedSize] || addon.prices?.['default'] || 0;
                  const isSelected = currentAddons.has(addon.id);

                  return (
                    <div key={addon.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex-1">
                        <span className="text-sm font-medium">{addon.name}</span>
                        <span className="text-xs text-muted-foreground ml-2">+{addonPrice} DA</span>
                      </div>
                      <Button
                        size="sm"
                        variant={isSelected ? "default" : "outline"}
                        onClick={() => {
                          const newAddons = new Set(currentAddons);
                          if (isSelected) {
                            newAddons.delete(addon.id);
                          } else {
                            newAddons.add(addon.id);
                          }
                          setUiState(prev => ({
                            ...prev,
                            selectedAddons: { ...prev.selectedAddons, [addonKey]: newAddons }
                          }));
                        }}
                      >
                        {isSelected ? <Check className="h-3 w-3" /> : <Plus className="h-3 w-3" />}
                      </Button>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Notes */}
          <div>
            <h4 className="font-medium mb-2">Notes</h4>
            <Textarea
              placeholder="Instructions spéciales..."
              value={currentNote}
              onChange={(e) => setUiState(prev => ({
                ...prev,
                itemNotes: { ...prev.itemNotes, [noteKey]: e.target.value }
              }))}
              className="min-h-[60px]"
            />
          </div>

          {/* Add to Cart Button */}
          <Button onClick={handleAddToCart} className="w-full">
            Ajouter au panier
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Order Type Selector Component
interface OrderTypeSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  orderState: OrderState;
  dispatch: React.Dispatch<OrderAction>;
  tables: Table[] | undefined;
  onContinue: () => void;
}

const OrderTypeSelector: React.FC<OrderTypeSelectorProps> = ({
  isOpen,
  onClose,
  orderState,
  dispatch,
  tables,
  onContinue
}) => {
  const [selectedTable, setSelectedTable] = useState<string>('');

  const orderTypes = [
    { type: 'dine-in' as OrderType, label: 'Sur Place', icon: Utensils, description: 'Service à table' },
    { type: 'takeaway' as OrderType, label: 'Emporter', icon: Package, description: 'À emporter' },
    { type: 'delivery' as OrderType, label: 'Livraison', icon: Truck, description: 'Livraison à domicile' }
  ];

  const handleContinue = () => {
    if (orderState.orderType === 'dine-in' && selectedTable) {
      dispatch({ type: 'SET_TABLE', payload: { tableId: selectedTable } });
    }
    onContinue();
  };

  const canContinue = orderState.orderType !== 'dine-in' || selectedTable;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Type de commande</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Order Type Selection */}
          <div className="grid grid-cols-1 gap-2">
            {orderTypes.map(({ type, label, icon: Icon, description }) => (
              <Button
                key={type}
                variant={orderState.orderType === type ? "default" : "outline"}
                onClick={() => dispatch({ type: 'SET_ORDER_TYPE', payload: { orderType: type } })}
                className="h-auto p-4 flex items-center justify-start gap-3"
              >
                <Icon className="h-5 w-5" />
                <div className="text-left">
                  <div className="font-medium">{label}</div>
                  <div className="text-xs opacity-70">{description}</div>
                </div>
              </Button>
            ))}
          </div>

          {/* Table Selection for Dine-in */}
          {orderState.orderType === 'dine-in' && (
            <div>
              <h4 className="font-medium mb-2">Sélectionner une table</h4>
              <div className="grid grid-cols-3 gap-2 max-h-40 overflow-y-auto">
                {tables?.map((table) => (
                  <Button
                    key={table.id}
                    variant={selectedTable === table.id ? "default" : "outline"}
                    onClick={() => setSelectedTable(table.id)}
                    className="h-12"
                  >
                    Table {table.name}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Continue Button */}
          <Button
            onClick={handleContinue}
            className="w-full"
            disabled={!canContinue}
          >
            Continuer
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Customer Info Modal Component
interface CustomerInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderState: OrderState;
  dispatch: React.Dispatch<OrderAction>;
  onContinue: () => void;
}

const CustomerInfoModal: React.FC<CustomerInfoModalProps> = ({
  isOpen,
  onClose,
  orderState,
  dispatch,
  onContinue
}) => {
  const [customerInfo, setCustomerInfo] = useState({
    name: orderState.customer?.name || '',
    phone: orderState.customer?.phone || '',
    address: orderState.customer?.address || ''
  });

  const handleContinue = () => {
    dispatch({ type: 'SET_CUSTOMER', payload: { customer: customerInfo } });
    onContinue();
  };

  const isDelivery = orderState.orderType === 'delivery';
  const canContinue = isDelivery ? customerInfo.phone && customerInfo.address : true;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Informations client</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Input
              placeholder="Nom (optionnel)"
              value={customerInfo.name}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
              className="text-base"
            />
          </div>

          <div>
            <Input
              placeholder={isDelivery ? "Téléphone *" : "Téléphone (optionnel)"}
              value={customerInfo.phone}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
              className="text-base"
            />
          </div>

          {isDelivery && (
            <div>
              <Textarea
                placeholder="Adresse de livraison *"
                value={customerInfo.address}
                onChange={(e) => setCustomerInfo(prev => ({ ...prev, address: e.target.value }))}
                className="min-h-[80px] text-base"
              />
            </div>
          )}

          <Button
            onClick={handleContinue}
            className="w-full"
            disabled={!canContinue}
          >
            Finaliser la commande
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Pizza Builder Modal Component (Simplified)
interface PizzaBuilderModalProps {
  isOpen: boolean;
  onClose: () => void;
  categories: Category[] | undefined;
  selectedCategory: string;
  uiState: UiState;
  setUiState: React.Dispatch<React.SetStateAction<UiState>>;
  dispatch: React.Dispatch<OrderAction>;
}

const PizzaBuilderModal: React.FC<PizzaBuilderModalProps> = ({
  isOpen,
  onClose,
  categories,
  selectedCategory,
  uiState,
  setUiState,
  dispatch
}) => {
  const currentCategory = categories?.find(cat => cat.id === selectedCategory);
  const availablePizzas = currentCategory?.items || [];

  const [selectedSize, setSelectedSize] = useState('default');
  const [notes, setNotes] = useState('');

  const filledQuarters = uiState.pizzaQuarters.filter(q => q !== null).length;

  const handleQuarterSelect = (quarterIndex: number, pizza: MenuItem) => {
    const price = pizza.prices[selectedSize] || Object.values(pizza.prices)[0] || 0;
    const quarter: PizzaQuarter = {
      id: pizza.id,
      name: pizza.name,
      price: price
    };

    const newQuarters = [...uiState.pizzaQuarters];
    newQuarters[quarterIndex] = quarter;

    setUiState(prev => ({ ...prev, pizzaQuarters: newQuarters }));
  };

  const handleConfirm = () => {
    const validQuarters = uiState.pizzaQuarters.filter(q => q !== null) as PizzaQuarter[];

    if (validQuarters.length > 0 && currentCategory) {
      dispatch({
        type: 'ADD_CUSTOM_PIZZA',
        payload: {
          quarters: validQuarters,
          size: selectedSize,
          notes: notes,
          categoryId: currentCategory.id,
          pricingMethod: uiState.pizzaPricingMethod
        }
      });

      // Reset pizza builder
      setUiState(prev => ({
        ...prev,
        pizzaQuarters: [null, null, null, null],
        showPizzaBuilder: false
      }));
      setNotes('');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Pizza Personnalisée</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Pizza Visual */}
          <div className="relative w-32 h-32 mx-auto">
            <svg viewBox="0 0 100 100" className="w-full h-full">
              <circle cx="50" cy="50" r="40" fill="#f3f4f6" stroke="#374151" strokeWidth="1"/>
              {[
                "M 50 50 L 50 10 A 40 40 0 0 1 90 50 Z", // Top Right
                "M 50 50 L 90 50 A 40 40 0 0 1 50 90 Z", // Bottom Right
                "M 50 50 L 50 90 A 40 40 0 0 1 10 50 Z", // Bottom Left
                "M 50 50 L 10 50 A 40 40 0 0 1 50 10 Z"  // Top Left
              ].map((path, index) => (
                <path
                  key={index}
                  d={path}
                  fill={uiState.pizzaQuarters[index] ? "#fbbf24" : "#e5e7eb"}
                  stroke="#374151"
                  strokeWidth="1"
                  className="cursor-pointer hover:opacity-80"
                />
              ))}
              <circle cx="50" cy="50" r="2" fill="#6b7280"/>
            </svg>
          </div>

          <div className="text-center text-sm text-muted-foreground">
            {filledQuarters}/4 quarts sélectionnés
          </div>

          {/* Size Selection */}
          <div>
            <h4 className="font-medium mb-2">Taille</h4>
            <div className="grid grid-cols-2 gap-2">
              {['default', 'large'].map((size) => (
                <Button
                  key={size}
                  variant={selectedSize === size ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedSize(size)}
                >
                  {size === 'default' ? 'Standard' : 'Grande'}
                </Button>
              ))}
            </div>
          </div>

          {/* Pizza Selection */}
          <div>
            <h4 className="font-medium mb-2">Sélectionner les pizzas</h4>
            <ScrollArea className="h-40">
              <div className="space-y-2">
                {availablePizzas.map((pizza) => (
                  <div key={pizza.id} className="flex items-center justify-between p-2 border rounded">
                    <span className="text-sm">{pizza.name}</span>
                    <div className="flex gap-1">
                      {[0, 1, 2, 3].map((quarterIndex) => (
                        <Button
                          key={quarterIndex}
                          size="sm"
                          variant="outline"
                          onClick={() => handleQuarterSelect(quarterIndex, pizza)}
                          className="h-6 w-6 p-0 text-xs"
                        >
                          {quarterIndex + 1}
                        </Button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Notes */}
          <div>
            <Textarea
              placeholder="Notes spéciales..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[60px]"
            />
          </div>

          {/* Confirm Button */}
          <Button
            onClick={handleConfirm}
            className="w-full"
            disabled={filledQuarters === 0}
          >
            Ajouter au panier
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
