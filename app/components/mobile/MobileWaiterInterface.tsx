"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { useTableDB } from "@/lib/hooks/useTableDB";
import { useMenuV4 } from "@/lib/hooks/use-menu-v4";
import { useOrderV4 } from "@/lib/hooks/use-order-v4";
import { cn } from "@/lib/utils";

// UI Components
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent, SheetTrigger, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";

// Icons
import { 
  Search, 
  ShoppingCart, 
  Plus, 
  Minus, 
  Table as TableIcon,
  X,
  ChevronRight,
  PlusCircle,
  Edit,
  Trash2,
  Pizza,
  Check
} from "lucide-react";

// Types and interfaces
import { OrderItem, Order } from "@/lib/types";
import type { MenuItem, Addon, Category } from "@/lib/db/v4-menu-service";
import { TableLayout as Table } from "@/lib/db/table-db";
import { useToast } from "@/components/ui/use-toast";
import type { PizzaQuarter } from "@/lib/types/pizza-types";

export default function MobileWaiterInterface() {
  // Auth and database hooks
  const { user } = useAuth();
  const { tables, isLoading: tablesLoading, error: tablesError, isReady: tablesReady } = useTableDB();
  const { categories, isLoading: menuLoading, error: menuError, isReady: menuReady } = useMenuV4();
  const { createOrder, isLoading: ordersLoading, error: ordersError, isReady: ordersReady } = useOrderV4();
  const { toast } = useToast();

  // UI State
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isTableDialogOpen, setIsTableDialogOpen] = useState(false);
  const [selectedItemForAddons, setSelectedItemForAddons] = useState<string | null>(null);
  const [selectedAddons, setSelectedAddons] = useState<{[key: string]: Set<string>}>({});
  const [itemNotes, setItemNotes] = useState<{[key: string]: string}>({});
  const [isOperationLoading, setIsOperationLoading] = useState(false);
  const [editingItem, setEditingItem] = useState<OrderItem | null>(null);

  // Order State
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [selectedItemSizes, setSelectedItemSizes] = useState<{[key: string]: string}>({});
  const [currentOrder, setCurrentOrder] = useState<Order>({
    id: "",
    type: "table",
    status: "pending",
    tableId: "",
    items: [],
    total: 0,
    createdAt: new Date()
  });

  // Pizza customization state
  const [isPizzaCustomizing, setIsPizzaCustomizing] = useState(false);
  const [pizzaQuarters, setPizzaQuarters] = useState<(PizzaQuarter | null)[]>([null, null, null, null]);
  const [pizzaPricingMethod, setPizzaPricingMethod] = useState<'max' | 'average'>('max');
  const [customizingPizzaItem, setCustomizingPizzaItem] = useState<MenuItem | null>(null);

  // Set initial category when categories load
  useEffect(() => {
    if (categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0].id);
    }
  }, [categories, selectedCategory]);

  // Helper functions
  const getAddonKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);

  const getAddonsForMenuItem = useCallback((itemId: string) => {
    for (const category of categories) {
      const item = category.items.find(i => i.id === itemId);
      if (item && item.addons) {
        return item.addons;
      }
    }
    return [];
  }, [categories]);

  const calculateItemPrice = useCallback((item: MenuItem, size: string, addons: Addon[] = []) => {
    const basePrice = item.prices[size] || 0;
    const addonPrice = addons.reduce((sum, addon) => sum + (addon.price || 0), 0);
    return basePrice + addonPrice;
  }, []);

  const calculateTotal = useCallback((items: OrderItem[]) => {
    return items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  }, []);

  // Handle adding item to order
  const handleAddItem = useCallback((item: MenuItem, size?: string) => {
    // Determine the correct size and price
    const selectedSize = size || Object.keys(item.prices)[0] || 'default';
    const price = item.prices[selectedSize] || Object.values(item.prices)[0] || 0;

    // Set the size selection for this item
    setSelectedItemSizes(prev => ({...prev, [item.id]: selectedSize}));

    // Get selected addons
    const addonKey = getAddonKey(item.id, selectedSize);
    const selectedAddonIds = Array.from(selectedAddons[addonKey] || new Set<string>());

    // Get addon details
    const availableAddons = getAddonsForMenuItem(item.id);
    const selectedAddonObjects = availableAddons
      .filter(addon => selectedAddonIds.includes(addon.id))
      .map(addon => ({
        id: addon.id,
        name: addon.name,
        price: addon.price
      }));

    // Get notes for this item
    const notes = itemNotes[item.id] || '';

    // Check if we're editing an existing item
    if (editingItem) {
      // Update the existing item
      const updatedItems = currentOrder.items.map(orderItem => {
        if (orderItem.id === editingItem.id) {
          return {
            ...orderItem,
            size: selectedSize,
            price: price,
            addons: selectedAddonObjects,
            notes: notes
          };
        }
        return orderItem;
      });

      setCurrentOrder(prev => ({
        ...prev,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      }));

      // Clear editing state
      setEditingItem(null);
      setSelectedItemForAddons(null);
      setSelectedAddons({});
      setItemNotes({});
      setSelectedItemSizes({});

      toast({
        title: "Item Updated",
        description: "Order item has been updated successfully.",
      });
    } else {
      // Check if identical item already exists
      const existingItemIndex = currentOrder.items.findIndex(orderItem => 
        orderItem.menuItemId === item.id &&
        orderItem.size === selectedSize &&
        JSON.stringify(orderItem.addons) === JSON.stringify(selectedAddonObjects) &&
        orderItem.notes === notes
      );

      if (existingItemIndex !== -1) {
        // Increment quantity of existing item
        const updatedItems = [...currentOrder.items];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + 1
        };

        setCurrentOrder(prev => ({
          ...prev,
          items: updatedItems,
          total: calculateTotal(updatedItems)
        }));
      } else {
        // Add new item
        const newItem: OrderItem = {
          id: `${Date.now()}-${Math.random()}`,
          menuItemId: item.id,
          name: item.name,
          price: price,
          quantity: 1,
          size: selectedSize,
          addons: selectedAddonObjects,
          notes: notes
        };

        const updatedItems = [...currentOrder.items, newItem];
        setCurrentOrder(prev => ({
          ...prev,
          items: updatedItems,
          total: calculateTotal(updatedItems)
        }));
      }
    }

    // Clear addon selection state
    setSelectedItemForAddons(null);
    setSelectedAddons({});
    setItemNotes({});
    setSelectedItemSizes({});
  }, [currentOrder.items, calculateItemPrice, calculateTotal, editingItem, getAddonKey, getAddonsForMenuItem, itemNotes, selectedAddons, selectedItemSizes, toast]);

  // Handle item customization
  const handleItemCustomization = useCallback((itemId: string) => {
    setSelectedItemForAddons(itemId);
    
    // Find the item
    const item = categories.flatMap(cat => cat.items).find(i => i.id === itemId);
    if (item) {
      const defaultSize = Object.keys(item.prices)[0] || 'default';
      setSelectedItemSizes(prev => ({...prev, [itemId]: defaultSize}));
    }
  }, [categories]);

  // Handle pizza customization
  const handlePizzaCustomization = useCallback((item: MenuItem) => {
    setCustomizingPizzaItem(item);
    setIsPizzaCustomizing(true);
    setPizzaQuarters([null, null, null, null]);
    
    const defaultSize = Object.keys(item.prices)[0] || 'default';
    setSelectedItemSizes(prev => ({...prev, [item.id]: defaultSize}));
  }, []);

  // Filter menu items based on search and category
  const filteredMenuItems = useMemo(() => {
    const category = categories.find(c => c.id === selectedCategory);
    if (!category) return [];

    return category.items.filter(item =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [categories, selectedCategory, searchQuery]);

  // Loading state
  if (!tablesReady || !menuReady || !ordersReady) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header */}
      <div className="flex-shrink-0 p-3 border-b bg-background">
        <div className="flex items-center gap-2 mb-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsTableDialogOpen(true)}
            className="h-8"
          >
            <TableIcon className="h-4 w-4 mr-1" />
            {selectedTable ? `Table ${selectedTable.name}` : 'Select Table'}
          </Button>
          
          <Sheet open={isCartOpen} onOpenChange={setIsCartOpen}>
            <SheetTrigger asChild>
              <Button size="sm" className="h-8 relative">
                <ShoppingCart className="h-4 w-4 mr-1" />
                Cart
                {currentOrder.items.length > 0 && (
                  <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                    {currentOrder.items.reduce((sum, item) => sum + item.quantity, 0)}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-full sm:max-w-md">
              <SheetHeader>
                <SheetTitle>Order Summary</SheetTitle>
              </SheetHeader>
              <div className="flex flex-col h-full">
                <ScrollArea className="flex-1 mt-4">
                  {currentOrder.items.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8">
                      <ShoppingCart className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>No items in cart</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {currentOrder.items.map((item) => (
                        <Card key={item.id} className="p-3">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="font-medium text-sm">{item.name}</h4>
                              {item.size && item.size !== 'default' && (
                                <p className="text-xs text-muted-foreground">Size: {item.size}</p>
                              )}
                              {item.addons && item.addons.length > 0 && (
                                <p className="text-xs text-muted-foreground">
                                  Addons: {item.addons.map(addon => addon.name).join(', ')}
                                </p>
                              )}
                              {item.notes && (
                                <p className="text-xs text-muted-foreground">Notes: {item.notes}</p>
                              )}
                              <p className="text-sm font-medium mt-1">{item.price} DA each</p>
                            </div>
                            <div className="flex flex-col items-end gap-2">
                              <div className="flex items-center gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    const newQuantity = item.quantity - 1;
                                    if (newQuantity <= 0) {
                                      const updatedItems = currentOrder.items.filter(i => i.id !== item.id);
                                      setCurrentOrder(prev => ({
                                        ...prev,
                                        items: updatedItems,
                                        total: calculateTotal(updatedItems)
                                      }));
                                    } else {
                                      const updatedItems = currentOrder.items.map(i =>
                                        i.id === item.id ? { ...i, quantity: newQuantity } : i
                                      );
                                      setCurrentOrder(prev => ({
                                        ...prev,
                                        items: updatedItems,
                                        total: calculateTotal(updatedItems)
                                      }));
                                    }
                                  }}
                                  className="h-6 w-6 p-0"
                                >
                                  <Minus className="h-3 w-3" />
                                </Button>
                                <span className="text-sm font-medium w-6 text-center">{item.quantity}</span>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    const updatedItems = currentOrder.items.map(i =>
                                      i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
                                    );
                                    setCurrentOrder(prev => ({
                                      ...prev,
                                      items: updatedItems,
                                      total: calculateTotal(updatedItems)
                                    }));
                                  }}
                                  className="h-6 w-6 p-0"
                                >
                                  <Plus className="h-3 w-3" />
                                </Button>
                              </div>
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => {
                                    // Edit item functionality
                                    const menuItem = categories.flatMap(cat => cat.items).find(mi => mi.id === item.menuItemId);
                                    if (menuItem) {
                                      setEditingItem(item);
                                      setSelectedItemForAddons(item.menuItemId);
                                      setSelectedItemSizes(prev => ({ ...prev, [item.menuItemId]: item.size || 'default' }));

                                      // Set addons
                                      if (item.addons && item.addons.length > 0) {
                                        const addonKey = getAddonKey(item.menuItemId, item.size || 'default');
                                        setSelectedAddons(prev => ({
                                          ...prev,
                                          [addonKey]: new Set(item.addons?.map(a => a.id) || [])
                                        }));
                                      }

                                      // Set notes
                                      if (item.notes) {
                                        setItemNotes(prev => ({ ...prev, [item.menuItemId]: item.notes || '' }));
                                      }

                                      setIsCartOpen(false);
                                    }
                                  }}
                                  className="h-6 w-6 p-0"
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => {
                                    const updatedItems = currentOrder.items.filter(i => i.id !== item.id);
                                    setCurrentOrder(prev => ({
                                      ...prev,
                                      items: updatedItems,
                                      total: calculateTotal(updatedItems)
                                    }));
                                  }}
                                  className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}
                </ScrollArea>

                {currentOrder.items.length > 0 && (
                  <div className="border-t pt-4 mt-4">
                    <div className="flex justify-between items-center mb-4">
                      <span className="font-semibold">Total:</span>
                      <span className="font-semibold text-lg">{currentOrder.total} DA</span>
                    </div>
                    <Button
                      onClick={async () => {
                        if (!selectedTable || currentOrder.items.length === 0) {
                          toast({
                            title: "Error",
                            description: "Please select a table and add items to the order.",
                            variant: "destructive"
                          });
                          return;
                        }

                        setIsOperationLoading(true);

                        try {
                          const newOrder = {
                            orderType: "dine-in" as const,
                            status: "pending" as const,
                            tableId: selectedTable.id,
                            items: currentOrder.items,
                            total: currentOrder.total,
                            notes: ""
                          };

                          await createOrder(newOrder);

                          toast({
                            title: "Order Placed",
                            description: `Order for table ${selectedTable.name} has been placed successfully.`,
                          });

                          // Reset order
                          setCurrentOrder({
                            id: "",
                            type: "table",
                            status: "pending",
                            tableId: "",
                            items: [],
                            total: 0,
                            createdAt: new Date()
                          });
                          setSelectedTable(null);
                          setSelectedItemForAddons(null);
                          setSelectedAddons({});
                          setItemNotes({});
                          setSelectedItemSizes({});
                          setIsCartOpen(false);

                        } catch (error) {
                          toast({
                            title: "Error",
                            description: "Failed to place order. Please try again.",
                            variant: "destructive"
                          });
                        } finally {
                          setIsOperationLoading(false);
                        }
                      }}
                      disabled={!selectedTable || isOperationLoading}
                      className="w-full"
                    >
                      {isOperationLoading ? "Placing Order..." : "Place Order"}
                    </Button>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search menu items..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-8"
          />
        </div>
      </div>

      {/* Categories */}
      <div className="flex-shrink-0 p-3 border-b">
        <ScrollArea className="w-full">
          <div className="flex gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="whitespace-nowrap h-8"
              >
                {category.name}
              </Button>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Menu Items */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-3 space-y-2">
            {filteredMenuItems.map((item) => {
              const sizes = Object.keys(item.prices);
              const defaultSize = sizes[0] || 'default';
              const defaultPrice = item.prices[defaultSize] || 0;
              const addons = getAddonsForMenuItem(item.id);
              const hasAddons = addons.length > 0;
              const isSelected = selectedItemForAddons === item.id;
              const selectedSize = selectedItemSizes[item.id] || defaultSize;
              const isPizza = item.name.toLowerCase().includes('pizza') || item.category?.toLowerCase().includes('pizza');

              return (
                <Card 
                  key={item.id} 
                  className={cn(
                    "cursor-pointer transition-all duration-200 hover:shadow-md",
                    isSelected && "ring-2 ring-primary"
                  )}
                  onClick={() => {
                    if (!isSelected && (hasAddons || sizes.length > 1)) {
                      handleItemCustomization(item.id);
                    }
                  }}
                >
                  <CardContent className={cn("p-3", !isSelected && "flex justify-between items-center")}>
                    {!isSelected ? (
                      <>
                        <div className="flex-1">
                          <div className="font-medium text-sm">{item.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {defaultPrice} DA
                          </div>
                          {item.description && (
                            <div className="text-xs text-muted-foreground mt-1 line-clamp-2">
                              {item.description}
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2">
                          {isPizza && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePizzaCustomization(item);
                              }}
                              className="h-8 w-8 p-0"
                            >
                              <Pizza className="h-4 w-4" />
                            </Button>
                          )}
                          {!hasAddons && sizes.length === 1 ? (
                            <Button
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAddItem(item, defaultSize);
                              }}
                              className="h-8 w-8 p-0"
                            >
                              <PlusCircle className="h-4 w-4" />
                            </Button>
                          ) : (
                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          )}
                        </div>
                      </>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium text-sm">{item.name}</h3>
                            <p className="text-xs text-muted-foreground">Customize your order</p>
                          </div>
                          <div className="font-medium text-sm">{item.prices[selectedSize]} DA</div>
                        </div>

                        {/* Size Selection */}
                        {sizes.length > 1 && (
                          <div className="pt-3 border-t">
                            <h4 className="text-sm font-medium mb-2">Size</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {sizes.map(size => {
                                const isSizeSelected = selectedItemSizes[item.id] === size;
                                return (
                                  <Button
                                    key={size}
                                    variant={isSizeSelected ? "default" : "outline"}
                                    size="sm"
                                    onClick={() => setSelectedItemSizes(prev => ({...prev, [item.id]: size}))}
                                    className="h-auto py-2 flex flex-col"
                                  >
                                    <span className="font-medium text-xs">{size}</span>
                                    <span className="text-xs opacity-80">{item.prices[size]} DA</span>
                                  </Button>
                                );
                              })}
                            </div>
                          </div>
                        )}

                        {/* Addons */}
                        {hasAddons && (
                          <div className="pt-3 border-t">
                            <h4 className="text-sm font-medium mb-2">Addons</h4>
                            <div className="space-y-2">
                              {addons.map(addon => {
                                const addonKey = getAddonKey(item.id, selectedSize);
                                const isAddonSelected = selectedAddons[addonKey]?.has(addon.id) || false;

                                return (
                                  <div key={addon.id} className="flex items-center justify-between p-2 border rounded">
                                    <div className="flex-1">
                                      <span className="text-sm font-medium">{addon.name}</span>
                                      <span className="text-xs text-muted-foreground ml-2">+{addon.price || 0} DA</span>
                                    </div>
                                    <Button
                                      size="sm"
                                      variant={isAddonSelected ? "default" : "outline"}
                                      onClick={() => {
                                        setSelectedAddons(prev => {
                                          const newAddons = { ...prev };
                                          if (!newAddons[addonKey]) {
                                            newAddons[addonKey] = new Set();
                                          }

                                          if (isAddonSelected) {
                                            newAddons[addonKey].delete(addon.id);
                                          } else {
                                            newAddons[addonKey].add(addon.id);
                                          }

                                          return newAddons;
                                        });
                                      }}
                                      className="h-8 w-8 p-0"
                                    >
                                      {isAddonSelected ? <Check className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                                    </Button>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )}

                        {/* Notes */}
                        <div className="pt-3 border-t">
                          <h4 className="text-sm font-medium mb-2">Notes</h4>
                          <Textarea
                            placeholder="Special instructions..."
                            value={itemNotes[item.id] || ""}
                            onChange={(e) => setItemNotes(prev => ({ ...prev, [item.id]: e.target.value }))}
                            className="min-h-[60px] text-sm"
                          />
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2 pt-3 border-t">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedItemForAddons(null);
                              setSelectedAddons({});
                              setItemNotes({});
                              setSelectedItemSizes({});
                            }}
                            className="flex-1"
                          >
                            Cancel
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleAddItem(item, selectedSize)}
                            className="flex-1"
                          >
                            {editingItem ? "Update Item" : "Add to Cart"}
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </ScrollArea>
      </div>

      {/* Table Selection Dialog */}
      <Dialog open={isTableDialogOpen} onOpenChange={setIsTableDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold">Select Table</h2>
            <ScrollArea className="max-h-60">
              <div className="grid grid-cols-2 gap-2">
                {tables.map((table) => (
                  <Button
                    key={table.id}
                    variant={selectedTable?.id === table.id ? "default" : "outline"}
                    onClick={() => {
                      setSelectedTable(table);
                      setCurrentOrder(prev => ({ ...prev, tableId: table.id }));
                      setIsTableDialogOpen(false);
                    }}
                    className="h-12"
                  >
                    Table {table.name}
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>

      {/* Pizza Customization Dialog */}
      <Dialog open={isPizzaCustomizing} onOpenChange={setIsPizzaCustomizing}>
        <DialogContent className="sm:max-w-lg">
          <div className="space-y-4">
            {customizingPizzaItem && (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Pizza className="h-5 w-5 text-primary" />
                    <h2 className="text-lg font-semibold">Pizza Personnalisée</h2>
                    <Badge variant="outline" className="text-xs">
                      {selectedItemSizes[customizingPizzaItem.id] === 'default' ? 'Classique' : selectedItemSizes[customizingPizzaItem.id]}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {pizzaQuarters.filter(q => q !== null).length}/4 quarts
                  </div>
                </div>

                {/* Size Selection for Pizza */}
                {Object.keys(customizingPizzaItem.prices).length > 1 && (
                  <div>
                    <h3 className="font-medium mb-2">Size</h3>
                    <div className="grid grid-cols-2 gap-2">
                      {Object.entries(customizingPizzaItem.prices).map(([size, price]) => (
                        <Button
                          key={size}
                          variant={selectedItemSizes[customizingPizzaItem.id] === size ? "default" : "outline"}
                          onClick={() => setSelectedItemSizes(prev => ({ ...prev, [customizingPizzaItem.id]: size }))}
                          className="h-12 flex flex-col"
                        >
                          <span className="font-medium">{size}</span>
                          <span className="text-xs">{price} DA</span>
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Pizza Visual */}
                <Card className="p-4">
                  <div className="relative w-48 h-48 mx-auto">
                    <svg viewBox="0 0 100 100" className="w-full h-full">
                      <circle cx="50" cy="50" r="40" fill="#f3f4f6" stroke="#374151" strokeWidth="1"/>

                      {/* Quarter segments */}
                      {[
                        "M 50 50 L 50 10 A 40 40 0 0 1 90 50 Z", // Top Right
                        "M 50 50 L 90 50 A 40 40 0 0 1 50 90 Z", // Bottom Right
                        "M 50 50 L 50 90 A 40 40 0 0 1 10 50 Z", // Bottom Left
                        "M 50 50 L 10 50 A 40 40 0 0 1 50 10 Z"  // Top Left
                      ].map((path, index) => (
                        <path
                          key={index}
                          d={path}
                          fill={pizzaQuarters[index] ? "#fbbf24" : "#e5e7eb"}
                          stroke="#374151"
                          strokeWidth="1"
                          className="cursor-pointer hover:opacity-80 transition-opacity"
                        />
                      ))}

                      <circle cx="50" cy="50" r="2" fill="#6b7280"/>
                    </svg>

                    {/* Quarter buttons */}
                    {[
                      { x: 65, y: 35 }, // Top Right
                      { x: 65, y: 65 }, // Bottom Right
                      { x: 35, y: 65 }, // Bottom Left
                      { x: 35, y: 35 }  // Top Left
                    ].map((pos, index) => (
                      <button
                        key={index}
                        className="absolute w-8 h-8 rounded-full bg-white/80 hover:bg-white border border-gray-300 shadow-sm flex items-center justify-center text-xs font-medium transition-all"
                        style={{
                          left: `${(pos.x / 100) * 192 - 16}px`,
                          top: `${(pos.y / 100) * 192 - 16}px`,
                        }}
                        onClick={() => {
                          // For now, just mark quarter as filled with current pizza
                          const newQuarters = [...pizzaQuarters];
                          newQuarters[index] = pizzaQuarters[index] ? null : {
                            menuItemId: customizingPizzaItem.id,
                            name: customizingPizzaItem.name,
                            price: customizingPizzaItem.prices[selectedItemSizes[customizingPizzaItem.id] || 'default'] || 0
                          };
                          setPizzaQuarters(newQuarters);
                        }}
                      >
                        {index + 1}
                      </button>
                    ))}
                  </div>
                </Card>

                {/* Pricing and Actions */}
                <div className="flex items-center justify-between bg-muted/50 p-3 rounded-lg">
                  <div>
                    <div className="text-sm text-muted-foreground">
                      Prix ({pizzaPricingMethod === 'max' ? 'Maximum' : 'Moyenne'})
                    </div>
                    <div className="text-lg font-bold">
                      {(() => {
                        const filledQuarters = pizzaQuarters.filter(q => q !== null) as PizzaQuarter[];
                        if (filledQuarters.length === 0) return 0;

                        if (pizzaPricingMethod === 'max') {
                          return Math.max(...filledQuarters.map(q => q.price));
                        } else {
                          return Math.round(filledQuarters.reduce((sum, q) => sum + q.price, 0) / filledQuarters.length);
                        }
                      })()} DA
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsPizzaCustomizing(false);
                        setCustomizingPizzaItem(null);
                        setPizzaQuarters([null, null, null, null]);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={() => {
                        // Add pizza to cart
                        if (customizingPizzaItem && pizzaQuarters.every(q => q !== null)) {
                          const size = selectedItemSizes[customizingPizzaItem.id] || 'default';
                          handleAddItem(customizingPizzaItem, size);
                          setIsPizzaCustomizing(false);
                          setCustomizingPizzaItem(null);
                          setPizzaQuarters([null, null, null, null]);
                        }
                      }}
                      disabled={!pizzaQuarters.every(q => q !== null)}
                      className="bg-primary hover:bg-primary/90"
                    >
                      Add Pizza
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
