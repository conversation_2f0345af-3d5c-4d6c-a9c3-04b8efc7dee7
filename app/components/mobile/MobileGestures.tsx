"use client";

import React, { useRef, useEffect, useCallback } from 'react';
import { useMobileLayout } from '@/hooks/use-mobile-layout';

// Types for gesture handling
interface SwipeGestureProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number;
  className?: string;
}

interface PullToRefreshProps {
  children: React.ReactNode;
  onRefresh: () => Promise<void>;
  threshold?: number;
  className?: string;
}

interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

// Haptic feedback simulation for web
export const hapticFeedback = {
  light: () => {
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate(10);
    }
  },
  medium: () => {
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate(20);
    }
  },
  heavy: () => {
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate([30, 10, 30]);
    }
  },
  success: () => {
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate([50, 25, 50]);
    }
  },
  error: () => {
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate([100, 50, 100, 50, 100]);
    }
  }
};

// Swipe gesture component
export const SwipeGesture: React.FC<SwipeGestureProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  threshold = 50,
  className
}) => {
  const { isMobile } = useMobileLayout();
  const touchStartRef = useRef<TouchPoint | null>(null);
  const touchEndRef = useRef<TouchPoint | null>(null);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!isMobile) return;
    
    const touch = e.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    };
  }, [isMobile]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (!isMobile || !touchStartRef.current) return;

    const touch = e.changedTouches[0];
    touchEndRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    };

    const deltaX = touchEndRef.current.x - touchStartRef.current.x;
    const deltaY = touchEndRef.current.y - touchStartRef.current.y;
    const deltaTime = touchEndRef.current.timestamp - touchStartRef.current.timestamp;

    // Only process quick swipes (less than 300ms)
    if (deltaTime > 300) return;

    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    // Determine swipe direction
    if (absDeltaX > threshold && absDeltaX > absDeltaY) {
      // Horizontal swipe
      if (deltaX > 0 && onSwipeRight) {
        hapticFeedback.light();
        onSwipeRight();
      } else if (deltaX < 0 && onSwipeLeft) {
        hapticFeedback.light();
        onSwipeLeft();
      }
    } else if (absDeltaY > threshold && absDeltaY > absDeltaX) {
      // Vertical swipe
      if (deltaY > 0 && onSwipeDown) {
        hapticFeedback.light();
        onSwipeDown();
      } else if (deltaY < 0 && onSwipeUp) {
        hapticFeedback.light();
        onSwipeUp();
      }
    }

    // Reset
    touchStartRef.current = null;
    touchEndRef.current = null;
  }, [isMobile, threshold, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown]);

  if (!isMobile) {
    return <div className={className}>{children}</div>;
  }

  return (
    <div
      className={className}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      style={{ touchAction: 'pan-y' }} // Allow vertical scrolling
    >
      {children}
    </div>
  );
};

// Pull to refresh component
export const PullToRefresh: React.FC<PullToRefreshProps> = ({
  children,
  onRefresh,
  threshold = 80,
  className
}) => {
  const { isMobile } = useMobileLayout();
  const containerRef = useRef<HTMLDivElement>(null);
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [pullDistance, setPullDistance] = React.useState(0);
  const touchStartRef = useRef<TouchPoint | null>(null);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!isMobile || isRefreshing) return;
    
    const container = containerRef.current;
    if (!container || container.scrollTop > 0) return;

    const touch = e.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    };
  }, [isMobile, isRefreshing]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isMobile || !touchStartRef.current || isRefreshing) return;

    const container = containerRef.current;
    if (!container || container.scrollTop > 0) return;

    const touch = e.touches[0];
    const deltaY = touch.clientY - touchStartRef.current.y;

    if (deltaY > 0) {
      // Pulling down
      const distance = Math.min(deltaY * 0.5, threshold * 1.5); // Damping effect
      setPullDistance(distance);
      
      if (distance > threshold * 0.7) {
        hapticFeedback.light();
      }
    }
  }, [isMobile, isRefreshing, threshold]);

  const handleTouchEnd = useCallback(async () => {
    if (!isMobile || !touchStartRef.current || isRefreshing) return;

    if (pullDistance > threshold) {
      setIsRefreshing(true);
      hapticFeedback.medium();
      
      try {
        await onRefresh();
        hapticFeedback.success();
      } catch (error) {
        hapticFeedback.error();
      } finally {
        setIsRefreshing(false);
      }
    }

    setPullDistance(0);
    touchStartRef.current = null;
  }, [isMobile, isRefreshing, pullDistance, threshold, onRefresh]);

  if (!isMobile) {
    return <div className={className}>{children}</div>;
  }

  return (
    <div
      ref={containerRef}
      className={className}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{ 
        transform: `translateY(${pullDistance}px)`,
        transition: isRefreshing || pullDistance === 0 ? 'transform 0.3s ease' : 'none'
      }}
    >
      {/* Pull to refresh indicator */}
      {pullDistance > 0 && (
        <div 
          className="absolute top-0 left-0 right-0 flex items-center justify-center py-2 text-sm text-muted-foreground"
          style={{ transform: `translateY(-${pullDistance}px)` }}
        >
          {isRefreshing ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
              <span>Actualisation...</span>
            </div>
          ) : pullDistance > threshold ? (
            <span>Relâchez pour actualiser</span>
          ) : (
            <span>Tirez pour actualiser</span>
          )}
        </div>
      )}
      
      {children}
    </div>
  );
};

// Long press gesture hook
export const useLongPress = (
  callback: () => void,
  duration: number = 500
) => {
  const { isMobile } = useMobileLayout();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const start = useCallback(() => {
    if (!isMobile) return;
    
    timeoutRef.current = setTimeout(() => {
      hapticFeedback.medium();
      callback();
    }, duration);
  }, [isMobile, callback, duration]);

  const clear = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  return {
    onTouchStart: start,
    onTouchEnd: clear,
    onTouchCancel: clear,
    onMouseDown: !isMobile ? start : undefined,
    onMouseUp: !isMobile ? clear : undefined,
    onMouseLeave: !isMobile ? clear : undefined,
  };
};

// Double tap gesture hook
export const useDoubleTap = (
  callback: () => void,
  delay: number = 300
) => {
  const { isMobile } = useMobileLayout();
  const lastTapRef = useRef<number>(0);

  const handleTap = useCallback(() => {
    const now = Date.now();
    const timeSinceLastTap = now - lastTapRef.current;

    if (timeSinceLastTap < delay && timeSinceLastTap > 0) {
      hapticFeedback.light();
      callback();
      lastTapRef.current = 0;
    } else {
      lastTapRef.current = now;
    }
  }, [callback, delay]);

  if (!isMobile) {
    return { onDoubleClick: callback };
  }

  return { onTouchEnd: handleTap };
};
