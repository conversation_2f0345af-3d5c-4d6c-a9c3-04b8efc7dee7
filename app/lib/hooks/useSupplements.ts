import { useState, useEffect, useCallback } from 'react';
import { Supplement } from '@/lib/db/v4/schemas/menu-schema';

interface UseSupplementsReturn {
  supplements: Supplement[];
  isLoading: boolean;
  error: string | null;
  isReady: boolean;
  createSupplement: (supplement: Omit<Supplement, 'id'>) => Promise<void>;
  updateSupplement: (id: string, updates: Partial<Supplement>) => Promise<void>;
  deleteSupplement: (id: string) => Promise<void>;
  refreshSupplements: () => Promise<void>;
}

export function useSupplements(categoryId: string): UseSupplementsReturn {
  const [supplements, setSupplements] = useState<Supplement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isReady, setIsReady] = useState(false);

  const loadSupplements = useCallback(async () => {
    if (!categoryId) {
      setSupplements([]);
      setIsLoading(false);
      setIsReady(true);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      const { getAllSupplements } = await import('@/lib/db/v4/operations/supplement-ops');
      const categorySupplements = await getAllSupplements(categoryId);
      
      setSupplements(categorySupplements || []);
      setIsReady(true);
    } catch (err) {
      console.error('Error loading supplements:', err);
      setError(err instanceof Error ? err.message : 'Failed to load supplements');
      setSupplements([]);
    } finally {
      setIsLoading(false);
    }
  }, [categoryId]);

  const createSupplement = useCallback(async (supplement: Omit<Supplement, 'id'>) => {
    try {
      const { createSupplement: createSupplementOp } = await import('@/lib/db/v4/operations/supplement-ops');
      await createSupplementOp(categoryId, supplement);
      await loadSupplements();
    } catch (err) {
      console.error('Error creating supplement:', err);
      throw err;
    }
  }, [categoryId, loadSupplements]);

  const updateSupplement = useCallback(async (id: string, updates: Partial<Supplement>) => {
    try {
      const { updateSupplement: updateSupplementOp } = await import('@/lib/db/v4/operations/supplement-ops');
      await updateSupplementOp(id, updates);
      await loadSupplements();
    } catch (err) {
      console.error('Error updating supplement:', err);
      throw err;
    }
  }, [loadSupplements]);

  const deleteSupplement = useCallback(async (id: string) => {
    try {
      const { deleteSupplement: deleteSupplementOp } = await import('@/lib/db/v4/operations/supplement-ops');
      await deleteSupplementOp(id);
      await loadSupplements();
    } catch (err) {
      console.error('Error deleting supplement:', err);
      throw err;
    }
  }, [loadSupplements]);

  const refreshSupplements = useCallback(async () => {
    await loadSupplements();
  }, [loadSupplements]);

  useEffect(() => {
    loadSupplements();
  }, [loadSupplements]);

  return {
    supplements,
    isLoading,
    error,
    isReady,
    createSupplement,
    updateSupplement,
    deleteSupplement,
    refreshSupplements,
  };
}