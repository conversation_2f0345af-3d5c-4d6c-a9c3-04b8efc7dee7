"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useRouter } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';
import { Tabs, TabsList, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { EditOrderProvider } from '@/components/providers/EditOrderContext';
import MobileWaiterInterface from '../../components/MobileWaiterInterface';
import OrderList from '../../components/OrderList';

export default function WaiterPage() {
  const { isAuthenticated, loading, user } = useAuth();
  const router = useRouter();
  const { navigate } = useStaticNavigation();
  const [tab, setTab] = useState('waiter');

  // Debug logging
  useEffect(() => {
    console.log('[WaiterPage] Auth state:', {
      isAuthenticated,
      loading,
      user: user ? {
        id: user.id,
        name: user.name,
        restaurantId: user.restaurantId,
        role: user.role
      } : null
    });
  }, [isAuthenticated, loading, user]);

  useEffect(() => {
    // Redirect to login if not authenticated and auth check is complete
    if (!loading && !isAuthenticated) {
      navigate('auth');
    }
  }, [isAuthenticated, loading, navigate]);

  // Show loading until auth is determined
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Only render the page when authenticated
  if (!isAuthenticated) return null;

  // Always use the new mobile-optimized interface
  return (
    <EditOrderProvider>
      <div className="h-screen flex flex-col">
        <Tabs value={tab} onValueChange={setTab} className="flex-grow flex flex-col">
          <div className="flex-shrink-0 flex justify-between items-center px-3 pt-3 pb-1 bg-background border-b">
            <TabsList className="touch-target">
              <TabsTrigger value="waiter" className="touch-target">Serveur</TabsTrigger>
              <TabsTrigger value="orders" className="touch-target">Commandes</TabsTrigger>
            </TabsList>
          </div>
          <TabsContent value="waiter" className="flex-grow overflow-hidden min-h-0">
            <MobileWaiterInterface />
          </TabsContent>
          <TabsContent value="orders" className="flex-grow overflow-hidden min-h-0">
            <OrderList setTab={setTab} />
          </TabsContent>
        </Tabs>
      </div>
    </EditOrderProvider>
  );
} 