/* Mobile Waiter Interface Optimizations */

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Smooth scrolling for mobile */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Hardware acceleration for animations */
.mobile-animate {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: transform;
}

/* Optimized transitions */
.mobile-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Button press feedback */
.mobile-button {
  transition: transform 0.1s ease, box-shadow 0.1s ease;
}

.mobile-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Card hover effects for mobile */
.mobile-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.mobile-card:active {
  transform: translateY(1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Prevent zoom on input focus */
@media screen and (max-width: 768px) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="tel"],
  input[type="number"],
  textarea,
  select {
    font-size: 16px !important;
    transform-origin: left top;
  }
}

/* Safe area handling */
.safe-top {
  padding-top: env(safe-area-inset-top);
}

.safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-left {
  padding-left: env(safe-area-inset-left);
}

.safe-right {
  padding-right: env(safe-area-inset-right);
}

.safe-x {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

.safe-y {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

/* Mobile-specific utilities */
.min-h-touch {
  min-height: 44px;
}

.min-w-touch {
  min-width: 44px;
}

/* Optimized grid layouts */
.mobile-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

@media screen and (max-width: 640px) {
  .mobile-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

/* Smooth category transitions */
.category-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading states */
.mobile-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Skeleton loading */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Improved focus states for accessibility */
.mobile-focus:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Optimized backdrop blur */
.mobile-backdrop {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.8);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .mobile-backdrop {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .mobile-animate,
  .mobile-transition,
  .category-transition,
  .mobile-button,
  .mobile-card {
    animation: none !important;
    transition: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .mobile-card {
    border-width: 2px;
  }
  
  .mobile-button {
    border-width: 2px;
  }
}

/* Print styles */
@media print {
  .mobile-waiter-interface {
    display: none;
  }
}

/* Landscape orientation optimizations */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .mobile-header {
    padding-top: 8px;
    padding-bottom: 8px;
  }
  
  .mobile-bottom-nav {
    padding-top: 8px;
    padding-bottom: 8px;
  }
}

/* Very small screens */
@media screen and (max-width: 320px) {
  .mobile-grid {
    gap: 0.5rem;
  }
  
  .mobile-padding {
    padding: 0.75rem;
  }
}

/* Large mobile screens */
@media screen and (min-width: 640px) and (max-width: 768px) {
  .mobile-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Performance optimizations */
.mobile-waiter-interface * {
  box-sizing: border-box;
}

.mobile-waiter-interface img {
  max-width: 100%;
  height: auto;
}

/* Prevent overscroll bounce on iOS */
.mobile-container {
  overscroll-behavior: contain;
}

/* Optimize rendering */
.mobile-layer {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* Smooth momentum scrolling */
.mobile-scroll-container {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* Fix for iOS Safari bottom bar */
@supports (-webkit-touch-callout: none) {
  .mobile-full-height {
    height: -webkit-fill-available;
  }
}
